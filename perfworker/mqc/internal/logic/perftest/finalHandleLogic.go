package perftest

import (
	"context"
	"os"
	"path/filepath"
	"time"

	"github.com/hashicorp/go-multierror"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common/util"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/svc"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type FinalHandleLogic struct {
	*BaseLogic

	option *FinalHandleLogicOption
}

type FinalHandleLogicOption struct {
	TaskID    string
	ExecuteID string
	ProjectID string
	PlanID    string
}

func NewFinalHandleLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, option *FinalHandleLogicOption,
) *FinalHandleLogic {
	result := &FinalHandleLogic{
		BaseLogic: NewBaseLogic(ctx, svcCtx, nil),

		option: option,
	}

	return result
}

func (l *FinalHandleLogic) Execute() (err error) {
	/**
	shutdown perf task
	1. delete job(perf_case_execute_id)
	2. clear perf data(perf_case_execute_id)
	*/

	l.Infof(
		"prepare to execute the final handle, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s",
		l.option.TaskID, l.option.ExecuteID, l.option.ProjectID, l.option.PlanID,
	)
	defer func() {
		if e := l.clearPerfData(); e != nil {
			l.Errorf(
				"failed to remove the perf data, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
				l.option.TaskID, l.option.ExecuteID, l.option.ProjectID, l.option.PlanID, e,
			)

			err = multierror.Append(err, e)
		}
	}()
	defer func() {
		// 发送压测完成通知
		l.sendFinishedNotification()
	}()
	defer func() {
		if e := l.updateAPIMetrics(); e != nil {
			l.Errorf(
				"failed to update the api metrics, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
				l.option.TaskID, l.option.ExecuteID, l.option.ProjectID, l.option.PlanID, e,
			)

			err = multierror.Append(err, e)
		}
	}()

	if err = l.deleteJob(); err != nil {
		l.Errorf(
			"failed to delete jobs, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
			l.option.TaskID, l.option.ExecuteID, l.option.ProjectID, l.option.PlanID, err,
		)
	}

	if err = l.deleteJobRelatedPod(); err != nil {
		l.Errorf(
			"failed to delete pods, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
			l.option.TaskID, l.option.ExecuteID, l.option.ProjectID, l.option.PlanID, err,
		)
	}

	return nil
}

func (l *FinalHandleLogic) deleteJob() error {
	jobs, err := l.ListJobWithLabel(map[string]string{"task-id": util.GenerateLabelKey(l.option.TaskID)})
	if err != nil {
		return errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to list job, task_id: %s execute_id: %s, error: %+v",
			l.option.TaskID, l.option.ExecuteID, err,
		)
	}

	for _, job := range jobs {
		err = l.DeleteJob(job.Name)
		if err != nil {
			l.Errorf(
				"failed to delete job, task_id: %s execute_id: %s, job: %s, error: %+v",
				l.option.TaskID, l.option.ExecuteID, job.Name, err,
			)
			continue
		}

		l.Infof(
			"delete job successfully, task_id: %s, execute_id: %s, job: %s",
			l.option.TaskID, l.option.ExecuteID, job.Name,
		)
	}

	return nil
}

func (l *FinalHandleLogic) deleteJobRelatedPod() error {
	pods, err := l.ListPodWithLabel(map[string]string{"task-id": util.GenerateLabelKey(l.option.TaskID)})
	if err != nil {
		return errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to list pod, task_id: %s execute_id: %s, error: %+v",
			l.option.TaskID, l.option.ExecuteID, err,
		)
	}

	for _, pod := range pods {
		err = l.DeletePod(pod.Name)
		if err != nil {
			l.Errorf(
				"failed to delete pod, task_id: %s, execute_id: %s, pod: %s, error: %+v",
				l.option.TaskID, l.option.ExecuteID, pod.Name, err,
			)
			continue
		}

		l.Infof(
			"delete pod successfully, task_id: %s, execute_id: %s, pod: %s",
			l.option.TaskID, l.option.ExecuteID, pod.Name,
		)
	}

	return nil
}

func (l *FinalHandleLogic) clearPerfData() (err error) {
	if l.option.TaskID == "" {
		return nil
	}

	err = os.RemoveAll(filepath.Join(PerfTaskDataPath, l.option.TaskID))
	if err != nil {
		return errorx.Errorf(
			errorx.FileOperationFailure,
			"failed to remove perf data, task_id: %s, execute_id: %s, error: %+v",
			l.option.TaskID, l.option.ExecuteID, err,
		)
	}

	l.Infof(
		"remove pref data successfully, task_id: %s, execute_id: %s", l.option.TaskID, l.option.ExecuteID,
	)
	return nil
}

func (l *FinalHandleLogic) updateAPIMetrics() error {
	out, err := l.svcCtx.ReporterRPC.GetPerfPlanRecord(
		l.ctx, &reporterpb.GetPerfPlanRecordReq{
			TaskId:    l.option.TaskID,
			ExecuteId: l.option.ExecuteID,
			ProjectId: l.option.ProjectID,
		},
	)
	if err != nil {
		return err
	}

	record := out.GetRecord()
	if record == nil {
		return errorx.Errorf(
			errorx.NotExists,
			"not found the perf plan execution record, task_id: %s, execute_id: %s, project_id: %s",
			l.option.TaskID, l.option.ExecuteID, l.option.ProjectID,
		)
	}

	startedAt := time.UnixMilli(record.GetStartedAt()).Add(-time.Minute).Local()
	endedAt := time.Now()
	if record.GetEndedAt() != 0 {
		endedAt = time.UnixMilli(record.GetEndedAt()).Add(time.Minute).Local()
	}

	apiMetrics, err := l.svcCtx.MonitorClient.QueryAPIMetrics(l.ctx, l.option.TaskID, startedAt, endedAt)
	if err != nil {
		return errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call api of monitor, task_id: %s, error: %+v",
			l.option.TaskID, err,
		)
	}

	_, err = l.svcCtx.ReporterRPC.ModifyPlanRecord(
		l.ctx, &reporterpb.ModifyPerfPlanRecordReq{
			TaskId:     record.GetTaskId(),
			ExecuteId:  record.GetExecuteId(),
			ProjectId:  record.GetProjectId(),
			PlanId:     record.GetPlanId(),
			Status:     record.GetStatus(),
			EndedAt:    timestamppb.New(endedAt),
			ApiMetrics: apiMetrics,
		},
	)

	return err
}

func (l *FinalHandleLogic) sendFinishedNotification() {
	var (
		taskID    = l.option.TaskID
		executeID = l.option.ExecuteID
		projectID = l.option.ProjectID
		planID    = l.option.PlanID
	)

	if _, err := l.svcCtx.DispatcherProducer.Send(
		l.ctx,
		base.NewTask(
			constants.MQTaskTypeDispatcherSendPerfNotification,
			protobuf.MarshalJSONIgnoreError(
				&dispatcherpb.PerfReportCallback{
					ProjectId:     projectID,
					TaskId:        taskID,
					PlanId:        planID,
					PlanExecuteId: executeID,
					Stage:         dispatcherpb.StageType_ST_FINISHED,
				},
			),
			base.WithMaxRetryOptions(0),
			base.WithRetentionOptions(5*time.Minute),
		),
		base.QueuePriorityDefault,
	); err != nil {
		l.Errorf(
			"failed to send perf finished notification task to dispatcher, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
			taskID, executeID, projectID, planID, err,
		)
	} else {
		l.Infof(
			"send perf finished notification task to dispatcher successfully, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s",
			taskID, executeID, projectID, planID,
		)
	}
}
