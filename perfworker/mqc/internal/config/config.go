package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	mqworkerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/constack"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/monitor"
)

type Config struct {
	service.ServiceConf

	Redis           redis.RedisKeyConf
	DispatcherRedis redis.RedisConf
	Cache           cache.CacheConf

	Account    zrpc.RpcClientConf
	Dispatcher zrpc.RpcClientConf
	Reporter   zrpc.RpcClientConf

	PerfWorkerConsumer consumerv2.Config
	PerfWorkerProducer mqworkerv2.Config
	DispatcherProducer mqworkerv2.Config
	AccountProducer    mqworkerv2.Config

	PerfToolConf PerfToolConf
	RunEnv       RunEnv

	GitLab     types.GitLabConfig
	Constack   constack.Config
	Monitor    monitor.Config
	AppInsight appInsight.Config
	StopRules  map[string]StopRule
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

func (c Config) ListenOn() string {
	return constants.NoNeedToListen
}
