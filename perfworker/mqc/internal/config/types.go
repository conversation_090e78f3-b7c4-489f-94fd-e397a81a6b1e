package config

import "time"

type (
	PerfToolConf struct {
		LogLevel        string
		RedisHost       string
		RedisType       string
		RedisPass       string
		RedisDb         int
		ConsumerBroker  string
		ConsumerBackend string
		ConsumerQueue   string
		ConsumerDb      int

		Image     string
		MountPath string

		ReporterTarget string
	}

	RunEnv struct {
		Cluster      string
		Namespace    string
		PvcName      string
		Env          string // prod, test
		IsServerless bool   // true为使用serverless容器, false为普通pod
		NfsServer    string `json:",optional"`
		NfsPath      string `json:",optional"`
	}

	StopRule struct {
		Threshold float64
		Duration  time.Duration `json:",default=1m"`
	}
)
