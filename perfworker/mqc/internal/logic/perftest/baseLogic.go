package perftest

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/constack/v1alpha"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	stopCh chan lang.PlaceholderType
}

func NewBaseLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, stopCh chan lang.PlaceholderType,
) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		stopCh: stopCh,
	}
}

func (l *BaseLogic) CreateJob(job *batchv1.Job) (err error) {
	data, _ := json.Marshal(job)
	_, err = l.svcCtx.ConstackGrpcClient.Create(
		l.ctx, &v1alpha.CreateRequest{
			Cluster:   l.svcCtx.Config.RunEnv.Cluster,
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
			Data:      string(data),
		},
	)
	if err != nil {
		l.Errorf(
			"[BaseLogic.CreateJob] create job:%s/%s/%s by call to constack failed, err: %v",
			l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, job.Name, err,
		)
		return fmt.Errorf(
			"[BaseLogic.CreateJob] create job:%s/%s/%s by call to constack failed, err: %v",
			l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, job.Name, err,
		)
	}

	return nil
}

func (l *BaseLogic) CreateServiceWithJob(job *batchv1.Job, ports ...int) (err error) {
	svcPorts := make([]corev1.ServicePort, 0)
	for _, port := range ports {
		svcPorts = append(
			svcPorts, corev1.ServicePort{
				Name:     fmt.Sprintf("port-%d", port),
				Protocol: corev1.ProtocolTCP,
				Port:     int32(port),
				TargetPort: intstr.IntOrString{
					Type:   0,
					IntVal: int32(port),
				},
			},
		)
	}

	k8sSvc := &corev1.Service{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Service",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      "perftool",
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
		},
		Spec: corev1.ServiceSpec{
			Ports:    svcPorts,
			Selector: map[string]string{"app": "perftool"},
		},
	}
	data, _ := json.Marshal(k8sSvc)
	_, err = l.svcCtx.ConstackGrpcClient.Apply(
		l.ctx, &v1alpha.ApplyRequest{
			Cluster:   l.svcCtx.Config.RunEnv.Cluster,
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
			Data:      string(data),
		},
	)
	if err != nil {
		l.Errorf(
			"[BaseLogic.CreateJob] apply svc:%s/%s/%s by call to constack failed, err: %v",
			l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, job.Name, err,
		)
		return fmt.Errorf(
			"[BaseLogic.CreateJob] apply svc:%s/%s/%s by call to constack failed, err: %v",
			l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, job.Name, err,
		)
	}

	return nil
}

func (l *BaseLogic) DeleteJob(jobName string) (err error) {
	_, err = l.svcCtx.ConstackGrpcClient.Delete(
		l.ctx, &v1alpha.DeleteRequest{
			Cluster:   l.svcCtx.Config.RunEnv.Cluster,
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
			Name:      jobName,
			GroupVersionResource: &v1alpha.GroupVersionResource{
				Group:    GroupJob,
				Version:  VersionJob,
				Resource: ResourceJob,
			},
		},
	)
	if err != nil {
		l.Errorf(
			"[BaseLogic.DeleteJob] delete job:%s/%s/%s by call to constack failed, err: %v",
			l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, jobName, err,
		)
		return fmt.Errorf(
			"[BaseLogic.DeleteJob] delete job:%s/%s/%s by call to constack failed, err: %v",
			l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, jobName, err,
		)
	}

	return nil
}

func (l *BaseLogic) DeletePod(podName string) (err error) {
	_, err = l.svcCtx.ConstackGrpcClient.Delete(
		l.ctx, &v1alpha.DeleteRequest{
			Cluster:   l.svcCtx.Config.RunEnv.Cluster,
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
			Name:      podName,
			GroupVersionResource: &v1alpha.GroupVersionResource{
				Group:    GroupPod,
				Version:  VersionPod,
				Resource: ResourcePod,
			},
		},
	)
	if err != nil {
		l.Errorf(
			"[BaseLogic.DeletePod] delete pod:%s/%s/%s by call to constack failed, err: %v",
			l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, podName, err,
		)
		return fmt.Errorf(
			"[BaseLogic.DeletePod] delete pod:%s/%s/%s by call to constack failed, err: %v",
			l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, podName, err,
		)
	}

	return nil
}

func (l *BaseLogic) GetJob(jobName string) (job *batchv1.Job, err error) {
	job = &batchv1.Job{}
	out, err := l.svcCtx.ConstackGrpcClient.Get(
		l.ctx, &v1alpha.GetRequest{
			Cluster:   l.svcCtx.Config.RunEnv.Cluster,
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
			Name:      jobName,
			GroupVersionResource: &v1alpha.GroupVersionResource{
				Group:    GroupJob,
				Version:  VersionJob,
				Resource: ResourceJob,
			},
		},
	)
	if err != nil {
		l.Errorf("[BaseLogic.GetJob] get job by call to constack failed, err:%v", err)
		return nil, fmt.Errorf("[BaseLogic.GetJob] get job by call to constack failed, err:%v", err)
	}

	err = json.Unmarshal([]byte(out.GetData()), job)
	if err != nil {
		l.Errorf("[BaseLogic.GetJob] convert job:%s, err:%v", jobName, err)
		return nil, fmt.Errorf("[BaseLogic.GetJob] get job by call to convert job:%s, err:%v", jobName, err)
	}

	return job, nil
}

func (l *BaseLogic) GetPod(podName string) (pod *corev1.Pod, err error) {
	pod = &corev1.Pod{}
	out, err := l.svcCtx.ConstackGrpcClient.Get(
		l.ctx, &v1alpha.GetRequest{
			Cluster:   l.svcCtx.Config.RunEnv.Cluster,
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
			Name:      podName,
			GroupVersionResource: &v1alpha.GroupVersionResource{
				Group:    GroupPod,
				Version:  VersionPod,
				Resource: ResourcePod,
			},
		},
	)
	if err != nil {
		l.Errorf("[BaseLogic.GetPod] get pods by call to constack failed, err:%v", err)
		return nil, fmt.Errorf("[ExecutePerfTestLogic.GetPod] get pods by call to constack failed, err:%v", err)
	}

	marshal, _ := json.Marshal(out)

	err = json.Unmarshal(marshal, pod)
	if err != nil {
		l.Errorf("[BaseLogic.GetPod] convert job:%s, err:%v", podName, err)
		return nil, fmt.Errorf("[ExecutePerfTestLogic.GetPod] get pods by call to convert job:%s, err:%v", podName, err)
	}

	return pod, nil
}

func (l *BaseLogic) ListPodWithJob(jobName string) (pods []*corev1.Pod, err error) {
	pods = make([]*corev1.Pod, 0)
	out, err := l.svcCtx.ConstackGrpcClient.List(
		l.ctx, &v1alpha.ListRequest{
			Cluster:   l.svcCtx.Config.RunEnv.Cluster,
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
			GroupVersionResource: &v1alpha.GroupVersionResource{
				Group:    GroupPod,
				Version:  VersionPod,
				Resource: ResourcePod,
			},
		},
	)
	if err != nil {
		l.Errorf("[BaseLogic.ListPodWithJob] list pods by call to constack failed, err:%v", err)
		return nil, fmt.Errorf("[BaseLogic.ListPodWithJob] list pods by call to constack failed, err:%v", err)
	}

	err = json.Unmarshal([]byte(out.GetData()), &pods)
	if err != nil {
		l.Errorf("[BaseLogic.ListPodWithJob] convert pods:%s, err:%v", jobName, err)
		return nil, fmt.Errorf("[BaseLogic.ListPodWithJob] list pods by call to convert job:%s, err:%v", jobName, err)
	}

	resultPods := make([]*corev1.Pod, 0, len(pods))

	for _, pod := range pods {
		if pod.GetLabels() == nil {
			continue
		}

		if pod.Labels["job-name"] == jobName {
			resultPods = append(resultPods, pod)
		}
	}

	return resultPods, nil
}

func (l *BaseLogic) ListJobWithLabel(label map[string]string) (jobs []*batchv1.Job, err error) {
	jobs = make([]*batchv1.Job, 0)
	out, err := l.svcCtx.ConstackGrpcClient.List(
		l.ctx, &v1alpha.ListRequest{
			Cluster:   l.svcCtx.Config.RunEnv.Cluster,
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
			GroupVersionResource: &v1alpha.GroupVersionResource{
				Group:    GroupJob,
				Version:  VersionJob,
				Resource: ResourceJob,
			},
		},
	)
	if err != nil {
		l.Errorf("[BaseLogic.ListPodWithJob] list jobs by call to constack failed, err:%v", err)
		return nil, fmt.Errorf("[BaseLogic.ListPodWithJob] list jobs by call to constack failed, err:%v", err)
	}

	err = json.Unmarshal([]byte(out.GetData()), &jobs)
	if err != nil {
		l.Errorf("[BaseLogic.ListJobWithLabel] convert err:%v", err)
		return nil, fmt.Errorf("[BaseLogic.ListJobWithLabel] list jobs by call to convert err:%v", err)
	}

	resultJobs := make([]*batchv1.Job, 0, len(jobs))

	for _, pod := range jobs {
		if pod.GetLabels() == nil {
			continue
		}

		// 完全匹配
		labelLen := 0
		for key, value := range label {
			if pod.GetLabels()[key] == value {
				labelLen++
			}
		}
		if labelLen == len(label) {
			resultJobs = append(resultJobs, pod)
		}
	}

	return resultJobs, nil
}

func (l *BaseLogic) ListPodWithLabel(label map[string]string) (pods []*corev1.Pod, err error) {
	pods = make([]*corev1.Pod, 0)
	out, err := l.svcCtx.ConstackGrpcClient.List(
		l.ctx, &v1alpha.ListRequest{
			Cluster:   l.svcCtx.Config.RunEnv.Cluster,
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
			GroupVersionResource: &v1alpha.GroupVersionResource{
				Group:    GroupPod,
				Version:  VersionPod,
				Resource: ResourcePod,
			},
		},
	)
	if err != nil {
		l.Errorf("[BaseLogic.ListPodWithLabel] list pods by call to constack failed, err:%v", err)
		return nil, fmt.Errorf("[BaseLogic.ListPodWithLabel] list pods by call to constack failed, err:%v", err)
	}

	err = json.Unmarshal([]byte(out.GetData()), &pods)
	if err != nil {
		l.Errorf("[BaseLogic.ListPodWithLabel] convert err:%v", err)
		return nil, fmt.Errorf("[BaseLogic.ListPodWithLabel] list pods by call to convert err:%v", err)
	}

	resultPods := make([]*corev1.Pod, 0, len(pods))

	for _, pod := range pods {
		if pod.GetLabels() == nil {
			continue
		}

		// 完全匹配
		labelLen := 0
		for key, value := range label {
			if pod.GetLabels()[key] == value {
				labelLen++
			}
		}
		if labelLen == len(label) {
			resultPods = append(resultPods, pod)
		}
	}

	return resultPods, nil
}

func (l *BaseLogic) CheckPvcExists(pvcName string) (isExist bool, err error) {
	out, err := l.svcCtx.ConstackGrpcClient.Get(
		l.ctx, &v1alpha.GetRequest{
			Cluster:   l.svcCtx.Config.RunEnv.Cluster,
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
			Name:      pvcName,
			GroupVersionResource: &v1alpha.GroupVersionResource{
				Group:    GroupPvc,
				Version:  VersionPvc,
				Resource: ResourcePvc,
			},
		},
	)
	if err != nil {
		return false, err
	}

	if out != nil {
		return true, nil
	}

	return false, nil
}

func (l *BaseLogic) WaitJobStatus(jobName string, timeout time.Duration) (err error) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	timer := time.NewTimer(timeout)
	defer timer.Stop()

	doneCh := make(chan error, 1) // 创建一个用于停止的通道
	defer close(doneCh)

	threading.GoSafe(
		func() {
			var err error
			defer func() {
				doneCh <- err
			}()

			for {
				select {
				case <-l.ctx.Done():
					l.Warnf("[BaseLogic.WaitJobStatus] context done")
					err = l.ctx.Err()
					return
				case <-l.stopCh:
					l.Warnf("[BaseLogic.WaitJobStatus] stop!")
					return
				case <-ticker.C: // 每当定时器触发时执行
					l.Infof("[BaseLogic.WaitJobStatus] ticker")
					success, err := l.CheckJobStatus(jobName)
					if err != nil {
						l.Warnf(
							"[BaseLogic.WaitJobStatus] check job status: %s/%s/%s error:%s",
							l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, jobName, err.Error(),
						)
						continue
					}

					if success {
						l.Infof("[BaseLogic.WaitJobStatus] job completed successfully")
						return
					}
				case <-timer.C: // 如果超时，则退出
					l.Warnf("[BaseLogic.WaitJobStatus] wait job timed out")
					err = errors.Errorf("wait job timeout: %s", timeout.String())
					return
				}
			}
		},
	)

	return <-doneCh
}

func (l *BaseLogic) WaitPodStarted(jobName string) (err error) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	timeout := time.After(10 * time.Minute) // 设置10分钟的超时
	doneCh := make(chan error)              // 创建一个用于停止的通道
	defer close(doneCh)

	go func() {
		for {
			select {
			case <-l.ctx.Done():
				l.Warnf("[BaseLogic.WaitPodStarted] context done")
				return
			case <-l.stopCh:
				l.Warnf("[BaseLogic.WaitPodStarted] stop!")
				return
			case <-ticker.C: // 每当定时器触发时执行
				l.Infof("[BaseLogic.WaitPodStarted] ticker")
				success, err := l.CheckPodStartedStatus(jobName)
				if err != nil {
					l.Warnf(
						"[BaseLogic.WaitPodStarted] check pod started status: %s/%s/%s error:%s",
						l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, jobName, err.Error(),
					)
					continue
				}

				if success {
					l.Infof("[BaseLogic.WaitPodStarted] pod has bean started")
					doneCh <- nil
					return
				}
			case <-timeout: // 如果超时，则退出
				l.Warnf("[BaseLogic.WaitPodStarted] wait pod started timed out")
				doneCh <- fmt.Errorf("wait pod started timed out")
				return
			}
		}
	}()

	return <-doneCh
}

func (l *BaseLogic) CheckJobStatus(jobName string) (bool, error) {
	job, err := l.GetJob(jobName)
	if err != nil {
		return false, err
	}

	pods, err := l.ListPodWithJob(jobName)
	if err != nil {
		return false, err
	}

	if len(pods) == 0 {
		return false, nil
	}

	l.Infof("[BaseLogic.CheckJobStatus] job:%s succeeded count: %d", jobName, job.Status.Succeeded)
	l.Infof("[BaseLogic.CheckJobStatus] job:%s related pod count: %d", jobName, len(pods))

	return int(job.Status.Succeeded) == len(pods), nil
}

func (l *BaseLogic) CheckPodStartedStatus(jobName string) (bool, error) {
	pods, err := l.ListPodWithJob(jobName)
	if err != nil {
		return false, err
	}

	for _, pod := range pods {
		// 有一个启动就算成功
		if pod.Status.Phase == corev1.PodRunning || pod.Status.Phase == corev1.PodSucceeded {
			return true, nil
		}
	}

	return false, nil
}
