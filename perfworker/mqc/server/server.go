package server

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/svc"
)

// NewConsumeServer for single server startup
func NewConsumeServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

// NewServer for combine server startup
func NewServer(c server.Config, w *log.ZapWriter) (service.Service, error) {
	cc, ok := c.(config.Config)
	if !ok {
		return nil, errors.Errorf("failed to new mqc server, cause by the config[%T] isn't a mqc config", c)
	}

	err := cc.ServiceConf.SetUp()
	if err != nil {
		return nil, errors.Errorf("failed to setup service config, error: %+v", err)
	}

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	ctx := svc.NewServiceContext(cc)

	if err = internal.SetupOperation(ctx, false); err != nil {
		return nil, errors.Errorf("failed to setup operation, error: %+v", err)
	}

	err = internal.TeardownOperation(ctx)
	if err != nil {
		return nil, err
	}

	return ctx.PerfWorkerConsumer, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c, conf.UseEnv())

	return c
}

// Options as a param of `server.NewServer`
func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(
			func() server.Config {
				return NewConfig(configFile)
			},
		),
		server.WithNewServiceFunc(NewServer),
	}
}
