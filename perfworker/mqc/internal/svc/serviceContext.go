package svc

import (
	"time"

	"github.com/hibiken/asynq"
	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	producerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
	constack "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/constack/v1alpha"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common/monitor"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common/zrpc/account"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common/zrpc/dispatcher"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common/zrpc/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	DB              sqlx.SqlConn
	Redis           *redis.Redis
	RedisNode       red.UniversalClient
	DispatcherRedis red.UniversalClient

	AccountRPC    *account.AccountRPC
	DispatcherRPC *dispatcher.DispatcherRPC
	ReporterRPC   *reporter.PerfReporterRPC

	Inspector          *asynq.Inspector
	PerfWorkerConsumer *consumerv2.Consumer
	PerfWorkerProducer *producerv2.Producer
	DispatcherProducer *producerv2.Producer
	AccountProducer    *producerv2.Producer

	ConstackGrpcClient constack.GRPCClient
	MonitorClient      *monitor.Client
	AppInsightClient   *appInsight.Client
}

func NewServiceContext(c config.Config) *ServiceContext {
	gCLi, err := constack.NewGRPCClient(c.Constack.Url, c.Constack.Token)
	if err != nil {
		return nil
	}

	return &ServiceContext{
		Config: c,

		Redis:           redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),
		RedisNode:       qetredis.NewClient(c.Redis.RedisConf),
		DispatcherRedis: qetredis.NewClient(c.DispatcherRedis),

		AccountRPC:    account.NewAccountRPC(c.Account),
		DispatcherRPC: dispatcher.NewDispatcherRPC(c.Dispatcher),
		ReporterRPC:   reporter.NewPerfReporterRPC(c.Reporter),

		Inspector:          newInspector(c.PerfWorkerProducer),
		PerfWorkerConsumer: consumerv2.NewConsumer(c.PerfWorkerConsumer),
		PerfWorkerProducer: producerv2.NewProducer(c.PerfWorkerProducer),
		DispatcherProducer: producerv2.NewProducer(c.DispatcherProducer),
		AccountProducer:    producerv2.NewProducer(c.AccountProducer),

		ConstackGrpcClient: gCLi,
		MonitorClient:      monitor.NewMonitorClient(c.Monitor),
		AppInsightClient:   appInsight.NewClient(c.AppInsight),
	}
}

func newInspector(c producerv2.Config) *asynq.Inspector {
	return asynq.NewInspector(
		asynq.RedisClientOpt{
			Addr:         c.Backend,
			DB:           c.Db,
			DialTimeout:  time.Second * 15,
			ReadTimeout:  time.Second * 15,
			WriteTimeout: time.Second * 15,
		},
	)
}
