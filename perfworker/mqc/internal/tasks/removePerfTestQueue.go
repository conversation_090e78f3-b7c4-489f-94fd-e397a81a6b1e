package tasks

import (
	"context"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/svc"
)

var _ base.Handler = (*ProcessorRemovePerfTestQueue)(nil)

type ProcessorRemovePerfTestQueue struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorRemovePerfTestQueue(svcCtx *svc.ServiceContext) base.Handler {
	return &ProcessorRemovePerfTestQueue{
		svcCtx: svcCtx,
	}
}

func (p *ProcessorRemovePerfTestQueue) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)

	for _, suffix := range []string{
		"",
		base.QueuePriorityDefault,
		base.QueuePriorityHigh,
		base.QueuePriorityUltra,
		base.QueuePriorityLow,
	} {
		queue := string(task.Payload)
		if suffix != "" && !strings.HasSuffix(queue, suffix) {
			queue = queue + "_" + suffix
		}

		if err = p.svcCtx.Inspector.DeleteQueue(queue, true); err != nil {
			logger.Warnf("failed to delete asynq queue, queue: %s, error: %+v", queue, err)
		}
	}

	return []byte(constants.SUCCESS), nil
}
