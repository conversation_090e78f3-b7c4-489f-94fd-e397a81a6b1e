package tasks

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/svc"
)

func TestExecutePerfTest_Execute(t *testing.T) {
	var c config.Config
	conf.MustLoad("../../etc/perfworker-cloud.yaml", &c)
	if err := c.ServiceConf.SetUp(); err != nil {
		t.Fatal(err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Minute)
	defer cancel()

	svcCtx := svc.NewServiceContext(c)

	processor := NewProcessorExecutePerfTest(svcCtx)

	type args struct {
		ctx  context.Context
		task *base.Task
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfWorkerExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&dispatcherpb.WorkerReq{
							TriggerMode: 0,
							TriggerRule: "",
							ProjectId:   "Kqllt5-9fA-I5UOdhjA5d",
							TaskId:      "",
							ExecuteId:   "",
							ExecuteType: 0,
							WorkerType:  0,
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId: "Kqllt5-9fA-I5UOdhjA5d",
								Verify:    false,
								Variables: nil,
							},
							AccountConfig: []*commonpb.AccountConfig{
								{
									ProjectId:    "",
									ConfigId:     "",
									Name:         "",
									Description:  "",
									ProductType:  0,
									ProductName:  "",
									PoolEnvTable: "t_tt_perf_1724407644405",
									PoolEnvName:  "",
								},
							},
							NodeData: &managerpb.ApiExecutionData{
								Id:   "zzzz",
								Type: 0,
								Data: &managerpb.ApiExecutionData_PerfCase{
									PerfCase: &managerpb.PerfCaseComponent{
										ProjectId: "Kqllt5-9fA-I5UOdhjA5d",
										CaseId:    "xxxxx",
										Path:      "/app/data",
										PerfData: &managerpb.PerfDataComponent{
											ProjectId:  "Kqllt5-9fA-I5UOdhjA5d",
											DataId:     "-ljnO0KW92vb7QmDk4poJ",
											Path:       "/app/data/perf_data",
											NumberOfVu: 300,
										},
										NumberOfVu: 10,
										LoadGenerator: &commonpb.LoadGenerator{
											NumberOfLg: 2,
										},
									},
								},
								Children:            nil,
								Error:               nil,
								ServiceCasesContent: nil,
							},
							PurposeType:  0,
							PriorityType: 0,
							Data: &dispatcherpb.WorkerReq_PerfCase{
								PerfCase: &dispatcherpb.PerfCaseWorkerInfo{
									PerfPlanId:        "sVrAd9GNmTiG-CtIOI4on",
									PerfCaseExecuteId: "qqqqqqqqq",
									MetaData: &managerpb.PerfPlanMetaData{
										ProtobufConfigs: []*commonpb.ProtobufConfig{
											{
												ProjectId:   "",
												ConfigId:    "",
												Name:        "",
												Description: "",
												GitConfig: &commonpb.GitConfig{
													ProjectId:   "",
													ConfigId:    "",
													Type:        "",
													Name:        "",
													Description: "",
													Url:         "/",
													AccessToken: "/",
													Branch:      "",
												},
												ImportPath:   "",
												ExcludePaths: nil,
												ExcludeFiles: nil,
												Dependencies: nil,
											},
										},
									},
								},
							},
							Debug: false,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
		{
			name: "test-fromAccount",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfWorkerExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&dispatcherpb.WorkerReq{
							TriggerMode: 0,
							TriggerRule: "",
							ProjectId:   "Kqllt5-9fA-I5UOdhjA5d",
							TaskId:      "",
							ExecuteId:   "",
							ExecuteType: 0,
							WorkerType:  0,
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId: "Kqllt5-9fA-I5UOdhjA5d",
								Verify:    false,
								Variables: nil,
							},
							AccountConfig: []*commonpb.AccountConfig{
								{
									ProjectId:    "",
									ConfigId:     "",
									Name:         "",
									Description:  "",
									ProductType:  0,
									ProductName:  "",
									PoolEnvTable: "t_tt_perf_1724407644405",
									PoolEnvName:  "",
								},
							},
							NodeData: &managerpb.ApiExecutionData{
								Id:   "zzzz",
								Type: 0,
								Data: &managerpb.ApiExecutionData_PerfCase{
									PerfCase: &managerpb.PerfCaseComponent{
										ProjectId: "Kqllt5-9fA-I5UOdhjA5d",
										CaseId:    "xxxxx",
										Path:      "/app/data",
										PerfData: &managerpb.PerfDataComponent{
											ProjectId: "Kqllt5-9fA-I5UOdhjA5d",
											// DataId:     "-ljnO0KW92vb7QmDk4poJ",
											Path:       "/app/data/perf_data",
											NumberOfVu: 300,
										},
										NumberOfVu: 10,
										LoadGenerator: &commonpb.LoadGenerator{
											NumberOfLg: 2,
										},
									},
								},
								Children:            nil,
								Error:               nil,
								ServiceCasesContent: nil,
							},
							PurposeType:  0,
							PriorityType: 0,
							Data: &dispatcherpb.WorkerReq_PerfCase{
								PerfCase: &dispatcherpb.PerfCaseWorkerInfo{
									PerfPlanId:        "sVrAd9GNmTiG-CtIOI4on",
									PerfCaseExecuteId: "qqqqqqqqq",
									MetaData: &managerpb.PerfPlanMetaData{
										ProtobufConfigs: []*commonpb.ProtobufConfig{
											{
												ProjectId:   "",
												ConfigId:    "",
												Name:        "",
												Description: "",
												GitConfig: &commonpb.GitConfig{
													ProjectId:   "",
													ConfigId:    "",
													Type:        "",
													Name:        "",
													Description: "",
													Url:         "/",
													AccessToken: "/",
													Branch:      "",
												},
												ImportPath:   "",
												ExcludePaths: nil,
												ExcludeFiles: nil,
												Dependencies: nil,
											},
										},
									},
								},
							},
							Debug: false,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := processor.ProcessTask(tt.args.ctx, tt.args.task)
				if (err != nil) != tt.wantErr {
					t.Errorf("ProcessTask() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("ProcessTask() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestTimeTruncate(t *testing.T) {
	now := time.Now()
	t.Logf("now: %s, %d\n", now.Format("2006-01-02 15:04:05"), now.Unix())

	now1 := now.Truncate(30 * time.Second)
	t.Logf("now1: %s, %d\n", now1.Format("2006-01-02 15:04:05"), now1.Unix())
}

func TestExecutePerfTestTaskLogic_watchMetrics(t *testing.T) {
	type args struct {
		rules    map[appInsight.MetricType]config.StopRule
		perfCase *commonpb.PerfCaseContentV2
		timeout  time.Duration
		now      time.Time
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "single step - single rule",
			args: args{
				rules: map[appInsight.MetricType]config.StopRule{
					appInsight.MetricTypeOfFailRatio: {
						Threshold: 0.15,
						Duration:  1 * time.Minute,
					},
				},
				perfCase: &commonpb.PerfCaseContentV2{
					SerialSteps: []*commonpb.PerfCaseStepV2{
						{
							Name:       "GetChannelSimpleInfo",
							Service:    "channel-go",
							Namespace:  "quicksilver",
							GrpcPath:   "/channel_go.ChannelGo/GetChannelSimpleInfo",
							Deprecated: true,
						},
					},
				},
				timeout: 10 * time.Minute,
				now:     time.Unix(1734491940, 0),
			},
		},
		{
			name: "multiple steps - multiple rules",
			args: args{
				rules: map[appInsight.MetricType]config.StopRule{
					appInsight.MetricTypeOfFailRatio: {
						Threshold: 0.15,
						Duration:  3 * time.Minute,
					},
					appInsight.MetricTypeOfP99: {
						Threshold: 200,
						Duration:  2 * time.Minute,
					},
				},
				perfCase: &commonpb.PerfCaseContentV2{
					SerialSteps: []*commonpb.PerfCaseStepV2{
						{
							Name:       "GetChannelSimpleInfo",
							Service:    "channel-go",
							Namespace:  "quicksilver",
							GrpcPath:   "/channel_go.ChannelGo/GetChannelSimpleInfo",
							Deprecated: true,
						},
						{
							Name:       "RecordUserLogin",
							Service:    "user-auth-history",
							Namespace:  "quicksilver",
							GrpcPath:   "/user_auth_history.UserAuthHistory/RecordUserLogin",
							Deprecated: true,
						},
					},
				},
				timeout: 5 * time.Minute,
			},
		},
		{
			name: "duplicated steps",
			args: args{
				rules: map[appInsight.MetricType]config.StopRule{
					appInsight.MetricTypeOfFailRatio: {
						Threshold: 0.15,
						Duration:  3 * time.Minute,
					},
					appInsight.MetricTypeOfP99: {
						Threshold: 200,
						Duration:  2 * time.Minute,
					},
				},
				perfCase: &commonpb.PerfCaseContentV2{
					SerialSteps: []*commonpb.PerfCaseStepV2{
						{
							Name:       "GetChannelSimpleInfo",
							Service:    "channel-go",
							Namespace:  "quicksilver",
							GrpcPath:   "/channel_go.ChannelGo/GetChannelSimpleInfo",
							Deprecated: true,
						},
						{
							Name:       "RecordUserLogin",
							Service:    "user-auth-history",
							Namespace:  "quicksilver",
							GrpcPath:   "/user_auth_history.UserAuthHistory/RecordUserLogin",
							Deprecated: true,
						},
					},
					ParallelSteps: []*commonpb.PerfCaseStepV2{
						{
							Name:       "RecordUserLogin",
							Service:    "user-auth-history",
							Namespace:  "quicksilver",
							GrpcPath:   "/user_auth_history.UserAuthHistory/RecordUserLogin",
							Deprecated: true,
						},
					},
				},
				timeout: 5 * time.Minute,
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				ctx, cancel := context.WithTimeout(context.Background(), tt.args.timeout)
				defer cancel()

				l := &ExecutePerfTestTaskLogic{
					Logger: logx.WithContext(ctx),
					ctx:    ctx,
					svcCtx: &svc.ServiceContext{
						AppInsightClient: appInsight.NewClient(
							appInsight.Config{
								BaseURL: "http://tt-telemetry-web.ttyuyin.com",
							},
						),
					},
					cancel:   cancel,
					perfCase: tt.args.perfCase,
					rules:    tt.args.rules,
				}

				ticker := timewheel.NewTicker(common.DefaultPeriodOfWatchServiceMetrics)
				defer ticker.Stop()

				t_ := tt.args.now
				if t_.IsZero() {
					t_ = time.Now().Truncate(30 * time.Second)
				}

				for {
					select {
					case <-l.ctx.Done():
						t.Log("got a done signal")
						return
					case <-ticker.C:
						if err := l.watchMetrics(t_); err != nil {
							t.Logf(
								"satisfy the stopping rules, now: %s, item: %s",
								t_.Format("2006-01-02 15:04:05"), jsonx.MarshalIgnoreError(l.alarmItem),
							)
							return
						}
					}

					t_ = t_.Add(common.DefaultPeriodOfWatchServiceMetrics)
				}
			},
		)
	}
}
