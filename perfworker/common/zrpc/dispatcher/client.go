package dispatcher

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/client/dispatcher"
)

type DispatcherRPC struct {
	conf zrpc.RpcClientConf

	client client.Dispatcher
}

func NewDispatcherRPC(c zrpc.RpcClientConf) *DispatcherRPC {
	return &DispatcherRPC{
		conf:   c,
		client: client.NewDispatcher(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *DispatcherRPC) Stop(ctx context.Context, in *client.StopReq, opts ...grpc.CallOption) (
	*client.StopResp, error,
) {
	return c.client.Stop(ctx, in, opts...)
}
