package common

import (
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/electricbubble/gadb"
	"github.com/pkg/errors"
)

// CreateTcpUsbBridgeOptions 配置选项
type CreateTcpUsbBridgeOptions struct {
	Port    int           // 要暴露的端口，默认5555
	Timeout time.Duration // 连接超时时间，默认30秒
}

// CreateTcpUsbBridgeOption 配置选项函数类型
type CreateTcpUsbBridgeOption func(*CreateTcpUsbBridgeOptions)

// WithPort 设置端口
func WithPort(port int) CreateTcpUsbBridgeOption {
	return func(opts *CreateTcpUsbBridgeOptions) {
		opts.Port = port
	}
}

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) CreateTcpUsbBridgeOption {
	return func(opts *CreateTcpUsbBridgeOptions) {
		opts.Timeout = timeout
	}
}

// CreateTcpUsbBridge 创建TCP-USB桥接
// 将通过USB连接的Android设备切换到TCP/IP模式，使其可以通过网络连接
// 参数:
//   - serial: 设备序列号，如果为空则使用第一个可用设备
//   - options: 配置选项
//
// 返回:
//   - string: 设备的网络地址 (IP:Port)
//   - error: 错误信息
func CreateTcpUsbBridge(serial string, options ...CreateTcpUsbBridgeOption) (string, error) {
	// 设置默认选项
	opts := &CreateTcpUsbBridgeOptions{
		Port:    5555,
		Timeout: 30 * time.Second,
	}

	// 应用用户配置
	for _, option := range options {
		option(opts)
	}

	// 验证端口范围
	if opts.Port < 1024 || opts.Port > 65535 {
		return "", errors.Errorf("invalid port: %d, port must be between 1024 and 65535", opts.Port)
	}

	// 创建ADB客户端
	client, err := gadb.NewClient()
	if err != nil {
		return "", errors.Wrap(err, "failed to create adb client")
	}

	// 启动ADB服务器
	if err = client.StartServer(); err != nil {
		return "", errors.Wrap(err, "failed to start adb server")
	}

	// 获取设备
	var device *gadb.Device
	if serial == "" {
		// 如果没有指定序列号，使用第一个可用设备
		devices, err := client.ListDevices()
		if err != nil {
			return "", errors.Wrap(err, "failed to list devices")
		}
		if len(devices) == 0 {
			return "", errors.New("no devices found")
		}
		device = devices[0]
		serial = device.Serial()
	} else {
		// 根据序列号查找设备
		device, err = client.FindDeviceBySerial(serial)
		if err != nil {
			return "", errors.Wrapf(err, "failed to find device by serial: %s", serial)
		}
	}

	// 检查设备是否可用（通过尝试执行一个简单命令）
	_, err = device.RunShellCommand("echo test")
	if err != nil {
		return "", errors.Wrapf(err, "device is not accessible: %s", serial)
	}

	// 切换设备到TCP/IP模式
	_, err = device.RunShellCommand(fmt.Sprintf("setprop service.adb.tcp.port %d", opts.Port))
	if err != nil {
		return "", errors.Wrapf(err, "failed to set adb tcp port to %d", opts.Port)
	}

	// 重启ADB守护进程以启用TCP/IP模式
	_, err = device.RunShellCommand("stop adbd")
	if err != nil {
		return "", errors.Wrap(err, "failed to stop adbd")
	}

	_, err = device.RunShellCommand("start adbd")
	if err != nil {
		return "", errors.Wrap(err, "failed to start adbd")
	}

	// 获取设备IP地址
	deviceIP, err := getDeviceIPAddress(device)
	if err != nil {
		return "", errors.Wrap(err, "failed to get device IP address")
	}

	// 构建网络地址
	networkAddress := fmt.Sprintf("%s:%d", deviceIP, opts.Port)

	// 等待TCP/IP模式生效并尝试连接
	if err = waitForTcpConnection(client, networkAddress, opts.Timeout); err != nil {
		return "", errors.Wrapf(err, "failed to establish tcp connection to %s", networkAddress)
	}

	return networkAddress, nil
}

// getDeviceIPAddress 获取设备IP地址
func getDeviceIPAddress(device *gadb.Device) (string, error) {
	// 尝试多种方式获取IP地址
	commands := []string{
		"ip route get ******* | head -1 | awk '{print $7}'",                    // 获取默认路由的源IP
		"ip addr show wlan0 | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1", // WiFi接口IP
		"getprop dhcp.wlan0.ipaddress",                                           // WiFi DHCP IP
		"ip addr show | grep 'inet.*global' | head -1 | awk '{print $2}' | cut -d'/' -f1", // 任意全局IP
	}

	for _, cmd := range commands {
		output, err := device.RunShellCommand(cmd)
		if err != nil {
			continue
		}

		ip := strings.TrimSpace(output)
		if ip != "" && net.ParseIP(ip) != nil {
			// 验证IP地址不是回环地址
			if !net.ParseIP(ip).IsLoopback() {
				return ip, nil
			}
		}
	}

	return "", errors.New("unable to determine device IP address")
}

// waitForTcpConnection 等待TCP连接可用
func waitForTcpConnection(client *gadb.Client, address string, timeout time.Duration) error {
	parts := strings.Split(address, ":")
	if len(parts) != 2 {
		return errors.Errorf("invalid address format: %s", address)
	}

	host := parts[0]
	port, err := strconv.Atoi(parts[1])
	if err != nil {
		return errors.Wrapf(err, "invalid port in address: %s", address)
	}

	// 等待一段时间让设备切换到TCP模式
	time.Sleep(2 * time.Second)

	// 设置超时
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		// 尝试连接到设备
		err = client.Connect(host, gadb.WithPort(port))
		if err == nil {
			// 连接成功，验证设备是否可用
			_, err = client.FindDeviceBySerial(address)
			if err == nil {
				return nil
			}
		}

		// 等待一段时间后重试
		time.Sleep(1 * time.Second)
	}

	return errors.Errorf("timeout waiting for tcp connection to %s", address)
}
