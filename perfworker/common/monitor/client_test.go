package monitor

import (
	"context"
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/monitor"
)

func TestClient_QueryAPIMetrics(t *testing.T) {
	// ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	// defer cancel()
	ctx := context.Background()

	c := NewMonitorClient(
		monitor.Config{
			BaseURL: "https://yw-hw-bj-sre-monitor-api.ttyuyin.com",
		},
	)
	type args struct {
		ctx       context.Context
		taskID    string
		startedAt time.Time
		endedAt   time.Time
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "query api metrics",
			args: args{
				ctx:    ctx,
				taskID: "task_id:1ZuhGMbdgje9PGV_wsbtz",
				startedAt: func() time.Time {
					t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-09 14:23:04", time.Local)
					return t_
				}(),
				endedAt: func() time.Time {
					t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-09 14:25:11", time.Local)
					return t_
				}(),
			},
			wantErr: false,
		},
		{
			name: "query api metrics with timestamp",
			args: args{
				ctx:    ctx,
				taskID: "task_id:ll_4FlhCW8QOwNqER4Ca6",
				startedAt: func() time.Time {
					t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-09-11 10:02:15", time.Local)
					return t_
				}(),
				endedAt: func() time.Time {
					ts := &timestamppb.Timestamp{
						Seconds: 1726020283,
						Nanos:   0,
					}
					return ts.AsTime().Local()
				}(),
			},
			wantErr: false,
		},
		{
			name: "query api metrics with multiple pods",
			args: args{
				ctx:    ctx,
				taskID: "task_id:4SADjluhqU6xgrR-8fimD",
				startedAt: func() time.Time {
					t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-10-10 16:48:51", time.Local)
					return t_
				}(),
				endedAt: func() time.Time {
					t_, _ := time.ParseInLocation("2006-01-02 15:04:05", "2024-10-10 16:54:27", time.Local)
					return t_
				}(),
			},
			wantErr: false,
		},
		{
			name: "",
			args: args{
				ctx:       ctx,
				taskID:    "task_id:5H4UkTL2KKLieJv0kvP12",
				startedAt: time.Date(2025, 3, 10, 16, 21, 4, 0, time.Local),
				endedAt:   time.Date(2025, 3, 10, 16, 28, 6, 0, time.Local),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := c.QueryAPIMetrics(tt.args.ctx, tt.args.taskID, tt.args.startedAt, tt.args.endedAt)
				if (err != nil) != tt.wantErr {
					t.Errorf("QueryAPIMetrics() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				t.Logf("APIMetrics: %s", protobuf.MarshalJSONWithMessagesToStringIgnoreError(got))
			},
		)
	}
}
