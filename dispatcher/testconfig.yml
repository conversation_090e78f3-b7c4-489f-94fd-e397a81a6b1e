---
broker: "127.0.0.1:6379"
# default_queue: default_queue
default_queue: machinery_tasks
result_backend: "127.0.0.1:6379"
# results_expire_in: 123456
# amqp:
#   binding_key: binding_key
#   exchange: exchange
#   exchange_type: exchange_type
#   prefetch_count: 123
#   queue_declare_args:
#     x-max-priority: 10
#   queue_binding_args:
#     image-type: png
#     x-match: any
# sqs:
#   receive_wait_time_seconds: 123
#   receive_visibility_timeout: 456
redis:
  max_idle: 12
  max_active: 123
  max_idle_timeout: 456
  wait: false
  read_timeout: 17
  write_timeout: 19
  connect_timeout: 21
  normal_tasks_poll_period: 1001
  delayed_tasks_poll_period: 500
  # delayed_tasks_key: delayed_tasks_key
  # master_name: master_name
# no_unix_signals: true
# dynamodb:
#   task_states_table: task_states_table
#   group_metas_table: group_metas_table
