package tasks

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/logic/perftest"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/svc"
)

var _ base.Handler = (*ProcessorFinalHandle)(nil)

type ProcessorFinalHandle struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorFinalHandle(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &ProcessorFinalHandle{
		svcCtx: svcCtx,
	}
}

func (processor *ProcessorFinalHandle) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	var info dispatcherpb.FinalHandleTaskInfo
	if err = protobuf.UnmarshalJSON(task.Payload, &info); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the final handle task payload[%s], error: %+v", task.Payload, err,
		)
	}

	if err = info.ValidateAll(); err != nil {
		return nil, errorx.Errorf(
			errorx.ValidateParamError,
			"failed to validate the final handle task payload[%s], error: %+v", task.Payload, err,
		)
	}

	err = perftest.NewFinalHandleLogic(
		ctx, processor.svcCtx, &perftest.FinalHandleLogicOption{
			TaskID:    info.GetTaskId(),
			ExecuteID: info.GetExecuteId(),
			ProjectID: info.GetProjectId(),
			PlanID:    info.GetPlanId(),
		},
	).Execute()
	if err != nil {
		return nil, err
	}

	return []byte(constants.SUCCESS), nil
}
