package perftest

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/url"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"
	"go.uber.org/atomic"
	"google.golang.org/protobuf/types/known/timestamppb"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	accountpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/calculate"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common/errorzh"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common/util"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/svc"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var (
	QueryAccountPoolColumns = []string{"account", "password", "tid", "uid", "my_channel_id"}
	volumeName              = "perf-data"
	jobNamePrefix           = "perftool"
)

type ExecutePerfTestLogic struct {
	*BaseLogic

	taskReq  *dispatcherpb.WorkerReq
	taskInfo *dispatcherpb.PerfTestTaskInfo
	perfCase *managerpb.PerfCaseComponent

	lock sync.Mutex

	protobufPath       string                                             // Deprecated: Protobuf项目路径
	protobufTargets    []*commonpb.ProtobufTarget                         // Protobuf项目目标
	perfCasePath       string                                             // 压测用例路径
	perfDataPaths      []string                                           // 压测数据路径
	totalOfVU          uint32                                             // 总的虚拟用户数
	releasePoolAccount []*accountpb.ReleaseTestAccountRequest_PoolAccount // 压测占用的数据

	state      *atomic.String
	apiMetrics []*reporterpb.APIMetric
	errMsg     *reporterpb.ErrorMessage

	queue    string
	taskName string
	timeout  uint32
	jobName  string

	rand_ *rand.Rand

	// stopCh chan lang.PlaceholderType
}

func NewExecutePerfTestLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, taskReq *dispatcherpb.WorkerReq,
	taskInfo *dispatcherpb.PerfTestTaskInfo, state *atomic.String, stopCh chan lang.PlaceholderType,
) *ExecutePerfTestLogic {
	var (
		executeID       = taskReq.GetExecuteId()
		executeIDSuffix = strings.TrimPrefix(executeID, commonutils.ConstExecuteIdPrefix)
		jobNameSuffix   = strings.Replace(strings.ToLower(executeIDSuffix), "_", "-", -1)
	)

	for strings.HasSuffix(jobNameSuffix, "-") {
		jobNameSuffix = strings.TrimSuffix(jobNameSuffix, "-")
	}

	queue := fmt.Sprintf(
		"%s|%s|%s",
		constants.MQTaskTypePerfToolExecutePerfTest, taskReq.GetTaskId(), taskReq.GetPerfCase().GetPerfCaseExecuteId(),
	)
	l := &ExecutePerfTestLogic{
		BaseLogic: NewBaseLogic(ctx, svcCtx, stopCh),

		taskReq:  taskReq,
		taskInfo: taskInfo,

		state: state,

		queue:    queue,
		taskName: queue,
		jobName:  fmt.Sprintf("%s-%s", jobNamePrefix, jobNameSuffix),

		rand_: rand.New(rand.NewSource(time.Now().UnixNano())),
	}

	// set pefCase
	if v, ok := taskReq.GetNodeData().GetData().(*managerpb.ApiExecutionData_PerfCase); ok {
		l.perfCase = v.PerfCase
	}

	l.state.Store(dispatcherpb.ComponentState_Started.String())
	return l
}

func (l *ExecutePerfTestLogic) Execute() (err error) {
	var (
		taskID    = l.taskInfo.GetTaskId()
		executeID = l.taskInfo.GetExecuteId()
		errCh     = make(chan error, 1)
	)

	defer func() {
		err = l.teardown(err)
	}()

	threading.GoSafe(
		func() {
			errCh <- l.execute()
		},
	)

	proc.AddShutdownListener(
		func() {
			l.Infof("receive shutdown callback, task_id: %s, execute_id: %s", taskID, executeID)
			errCh <- errorzh.ErrZhPerfWorkerShutdown(errors.New("failed to continue execute due to shutdown"))
			l.Infof("prepare to delete the job in shutdown callback, job_name: %s", l.jobName)
			if err := l.DeleteJob(l.jobName); err != nil {
				l.Errorf("failed to delete the job in shutdown callback, error: %+v", err)
			}
		},
	)

	select {
	case <-l.ctx.Done():
		l.Warnf(
			"got a stop signal while executing the perf test from context done, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, l.ctx.Err(),
		)
		return nil
	case <-l.stopCh:
		l.Warnf(
			"got a stop signal while executing the perf test from stopCh, task_id: %s, execute_id: %s",
			taskID, executeID,
		)
		return nil
	case err = <-errCh:
		return err
	}
}

func (l *ExecutePerfTestLogic) execute() error {
	/**
	  1. 检查pvc是否存在
	  2. 拉pb
	  3. 生成测试数据
	  4. 创建job
	  5. 向job推送数据
	*/

	var (
		taskID        = l.taskReq.GetTaskId()
		executeID     = l.taskReq.GetExecuteId()
		planExecuteID = l.taskReq.GetPerfCase().GetPerfPlanExecuteId()
		projectID     = l.taskReq.GetProjectId()
		planID        = l.taskReq.GetPerfCase().GetPerfPlanId()
	)

	l.Infof("prepare to execute the perf task, task_id: %s, execute_id: %s", taskID, executeID)

	exists, err := l.CheckPvcExists(l.svcCtx.Config.RunEnv.PvcName)
	if err != nil {
		l.Errorf(
			"failed to check pvc, task_id: %s, execute_id: %s, pvc: %s, error: %+v",
			taskID, executeID, l.svcCtx.Config.RunEnv.PvcName, err,
		)
		return errorzh.ErrZhPVCCheckFailed(err)
	}
	if !exists {
		return errorzh.ErrZhPVCNotFound(fmt.Errorf("pvc[%s] not found", l.svcCtx.Config.RunEnv.PvcName))
	}

	l.Infof("prepare to pull protobuf, task_id: %s, execute_id: %s", taskID, executeID)
	if err = l.pullProtobuf(); err != nil {
		l.Errorf(
			"failed to pull protobuf, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
		return errorzh.ErrZhProtoPullFailed(err)
	}

	l.Infof("prepare to generate perf data, task_id: %s, execute_id: %s", taskID, executeID)
	if err = l.generatePerfData(); err != nil {
		l.Errorf(
			"failed to generate perf data, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
		return errorzh.ErrZhPerfDataGenFailed(err)
	}

	//l.Infof("prepare to generate perf case, task_id: %s, execute_id: %s", taskID, executeID)
	//if err = l.generatePerfCase(); err != nil {
	//	l.Errorf(
	//		"failed to generate perf case, task_id: %s, execute_id: %s, error: %+v",
	//		taskID, executeID, err,
	//	)
	//	return err
	//}

	if err = l.calculateTimeout(); err != nil {
		l.Errorf(
			"failed to calculate the timeout of perf task, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
		return err
	}

	// jobName := fmt.Sprintf("%s-%d", "perftool", l.rand_.Int63n(90000000)+10000000)
	jobName := l.jobName
	job := l.generateBizJob(jobName)
	job.Labels["execute-id"] = util.GenerateLabelKey(l.taskReq.GetExecuteId())
	job.Labels["task-id"] = util.GenerateLabelKey(l.taskReq.GetTaskId())

	l.Infof("prepare to create job:%s, task_id: %s, execute_id: %s", jobName, taskID, executeID)
	if err = l.CreateJob(job); err != nil {
		l.Errorf(
			"failed to create job, task_id: %s, execute_id: %s, job: %s/%s/%s, error: %+v",
			taskID, executeID, l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, jobName, err,
		)
		return errorzh.ErrZhJobCreateFailed(err)
	}

	if err = l.CreateServiceWithJob(job, 9101); err != nil {
		l.Warnf(
			"failed to create service, task_id: %s, execute_id: %s, svc: %s/%s/%s, error: %+v",
			taskID, executeID, l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, jobName, err,
		)
	}

	if err = l.sendTaskInfoToStartedPod(jobName); err != nil {
		l.Errorf(
			"failed to send tasks to started pods, task_id: %s, execute_id: %s, job: %s/%s/%s, error: %+v",
			taskID, executeID, l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, jobName, err,
		)
		return errorzh.ErrZhPerfTaskSendFailed(err)
	}

	// update plan reporter
	if err = l.sendTaskResultToPerfPlanReporter(); err != nil {
		l.Infof(
			"failed to send task result to reporter, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
		return errorzh.ErrZhPerfResultReportFailed(err)
	}

	if l.taskReq.GetPerfCase().GetPerfPlanInfo().GetSendPreviewNotification() {
		// 发送压测开始通知
		l.sendStartedNotification()
	} else {
		l.Infof(
			"no need to send perf started notification task to dispatcher, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s",
			taskID, planExecuteID, projectID, planID,
		)
	}

	l.Infof("prepare to wait job status, task_id: %s, execute_id: %s", taskID, executeID)

	// wait job status
	if err = l.WaitJobStatus(jobName, time.Duration(l.timeout)*time.Second); err != nil {
		l.Errorf(
			"failed to wait job status, task_id: %s, execute_id: %s, job: %s/%s/%s, error: %+v",
			taskID, executeID, l.svcCtx.Config.RunEnv.Cluster, l.svcCtx.Config.RunEnv.Namespace, jobName, err,
		)
		return errorzh.ErrZhJobWaitCompletedFailed(err)
	}

	l.Infof("prepare to summary tool results, task_id: %s, execute_id: %s", taskID, executeID)

	// summary tool results
	if err = l.ToolResultsSummary(); err != nil {
		l.Errorf(
			"failed to summary tool results, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, err,
		)
		return err
	}

	l.Infof("finish to execute the perf task, task_id: %s, execute_id: %s", taskID, executeID)
	return nil
}

func (l *ExecutePerfTestLogic) teardown(err error) error {
	var (
		taskID    = l.taskInfo.GetTaskId()
		executeID = l.taskInfo.GetExecuteId()
	)

	// ignore cancel and deadlineExceed error
	if err != nil && !(errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded)) {
		errZh, ok := err.(errorzh.ErrZh)
		if ok {
			l.state.Store(errZh.State().String())
		} else {
			l.state.Store(dispatcherpb.ComponentState_Panic.String())
			errZh = errorzh.ErrZhOthersFailed(err)
		}
		l.errMsg = &reporterpb.ErrorMessage{
			MessageEn: errZh.Error(),
			MessageZh: string(errZh.ErrorZh()),
		}
	} else {
		l.state.CompareAndSwap(constants.PerfExecuteStateRunning.String(), dispatcherpb.ComponentState_Success.String())
	}

	e := l.sendTaskResultToPerfCaseReporter()
	if e != nil {
		l.Errorf(
			"[ExecutePerfTestLogic.Execute] failed to send task result to reporter, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, e,
		)

		if err != nil {
			err = multierror.Append(err, e)
		}
		return err
	}

	e = l.sendTaskCallbackDataToDispatcher()
	if e != nil {
		l.Errorf(
			"[ExecutePerfTestLogic.Execute] failed to send task callback data to dispatcher, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, e,
		)

		if err != nil {
			err = multierror.Append(err, e)
		}
		return err
	}

	l.Infof("prepare to release accounts, task_id: %s, execute_id: %s", taskID, executeID)

	// release account
	e = l.releaseTestAccounts()
	if e != nil {
		l.Errorf(
			"[ExecutePerfTestLogic.Execute] failed to release accounts, task_id: %s, execute_id: %s, error: %+v",
			taskID, executeID, e,
		)

		if err != nil {
			err = multierror.Append(err, e)
		}
		return err
	}

	return err
}

func (l *ExecutePerfTestLogic) pullProtobuf() error {
	ctx, cancel := context.WithTimeout(l.ctx, defaultPullProtobufTimeout)
	defer cancel()

	basePath := filepath.Join(PerfTaskDataPath, ProtobufPath)
	protobufConfigs := l.taskReq.GetPerfCase().GetMetaData().GetProtobufConfigs()
	if l.protobufTargets == nil {
		l.protobufTargets = make([]*commonpb.ProtobufTarget, 0, len(protobufConfigs))
	}

	for _, protobufConfig := range protobufConfigs {
		gitConfig := protobufConfig.GetGitConfig()
		if gitConfig == nil {
			continue
		}

		path := filepath.Join(basePath, gitConfig.GetConfigId())
		if err := l.pullByGitConfig(ctx, path, gitConfig); err != nil {
			return err
		}

		importPaths := make([]string, 0, len(protobufConfig.GetDependencies()))
		for _, dep := range protobufConfig.GetDependencies() {
			gc := dep.GetGitConfig()
			if gc == nil {
				continue
			}

			importPath := filepath.Join(basePath, gc.GetConfigId())
			if err := l.pullByGitConfig(ctx, importPath, gc); err != nil {
				return err
			}

			importPaths = append(importPaths, importPath)
		}

		l.protobufTargets = append(
			l.protobufTargets, &commonpb.ProtobufTarget{
				Path:         path,
				ImportPaths:  importPaths,
				ExcludePaths: protobufConfig.GetExcludePaths(),
				ExcludeFiles: protobufConfig.GetExcludeFiles(),
			},
		)
	}

	return nil
}

func (l *ExecutePerfTestLogic) pullByGitConfig(
	ctx context.Context, path string, config *commonpb.GitConfig,
) (err error) {
	if ctx == nil {
		ctx = l.ctx
	}

	lockKey := fmt.Sprintf("%s:%s", pullProtobufLockKey, config.GetConfigId())
	chanName := fmt.Sprintf("%s:%s", pullProtobufChannelName, config.GetConfigId())

	lock, err := redislock.NewRedisLockAndAcquire(
		l.svcCtx.Redis, lockKey, redislock.WithExpire(defaultPullProtobufTimeout),
	)
	if err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			l.Errorf("failed to acquire the redis lock, key: %s, error: %+v", lockKey, err)
			return err
		}

		l.Infof("another service is executing pull protobuf, wait for the execution result, path: %s", path)
		ps := l.svcCtx.RedisNode.Subscribe(ctx, chanName)
		defer func(ps *redis.PubSub) {
			e := ps.Close()
			if e != nil {
				l.Errorf("failed to close PubSub, path: %s, error: %+v", path, e)
			}
		}(ps)

		select {
		case msg, ok := <-ps.Channel():
			if ok {
				l.Infof("got a result of pulling protobuf, path: %s, result: %+v", path, msg)
				if msg.Payload != constants.SUCCESS {
					err = errors.Errorf("failed to pull protobuf, path: %s, error: %s", path, msg.Payload)
				} else {
					err = nil
				}
			} else {
				err = errors.Errorf("the channel of PubSub has been closed, path: %s, chan: %s", path, chanName)
			}
		case <-ctx.Done():
			err = errors.Errorf(
				"waiting for the result of pulling protobuf timed out, path: %s, error: %+v", path, ctx.Err(),
			)
		}

		return err
	}
	defer func() {
		e := lock.Release()
		if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
			err = e
		}
	}()

	result := constants.SUCCESS
	err = l.cloneOrPullByGitConfig(ctx, path, config)
	if err != nil {
		result = err.Error()
		err = errors.Errorf("failed to pull protobuf, path: %s, error: %+v", path, err)
	}

	if val, e := l.svcCtx.RedisNode.Publish(ctx, chanName, result).Result(); e != nil {
		l.Errorf(
			"failed to send the result of pulling protobuf to the channel, path: %s, chan: %s, error: %+v",
			path, chanName, err,
		)
	} else {
		l.Infof(
			"succeeded to send the result of pulling protobuf to the channel, path: %s, chan: %s, number of subscriber: %d",
			path, chanName, val,
		)
	}

	return err
}

func (l *ExecutePerfTestLogic) cloneOrPullByGitConfig(
	ctx context.Context, path string, config *commonpb.GitConfig,
) error {
	var (
		gitURL         = config.GetUrl()
		gitAccessToken = config.GetAccessToken()
		gitBranch      = config.GetBranch()

		u      *url.URL
		commit *object.Commit
		err    error
	)

	if ctx == nil {
		ctx = l.ctx
	}

	token := gitAccessToken
	if token == "" {
		token = l.svcCtx.Config.GitLab.Token
	}

	u, err = url.Parse(gitURL)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.ValidateParamError, err.Error()),
			"failed to parse the git url, url: %s, error: %+v",
			gitURL, err,
		)
	} else if _, ok := u.User.Password(); !ok || u.User.Username() == "" {
		u.User = url.UserPassword(constants.ConstDefaultGitUsername, token)
	}
	gitURLWithAuth := u.String()

	if utils.Exists(path) {
		// git pull
		commit, err = utils.PullWithContext(ctx, path, utils.WithPullUseCommand())
	} else {
		// git clone
		commit, err = utils.CloneWithContext(ctx, gitURLWithAuth, path, gitBranch, utils.WithCloneUseCommand())
	}
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.GitOperationFailure, err.Error()),
			"failed to clone or pull the git repo, git: %s, branch: %s, path: %s, error: %+v",
			gitURL, gitBranch, path, err,
		)
	}

	l.Infof(
		"finish to clone or pull the git repo, git: %s, path: %s, branch: %s, commit: %q",
		gitURL, path, gitBranch, commit.String(),
	)
	return nil
}

// generatePerfData 生成测试数据, 这里需要从目录中读取到测试数据并将其拆分成多个文件
func (l *ExecutePerfTestLogic) generatePerfData() (err error) {
	subPath := make([]string, 0, l.perfCase.GetLoadGenerator().GetNumberOfLg())
	totalLine := 0
	defer func() {
		if err == nil {
			l.perfDataPaths = subPath
			l.totalOfVU = uint32(totalLine)
		}
	}()

	for i := 0; i < int(l.perfCase.GetLoadGenerator().GetNumberOfLg()); i++ {
		subPath = append(
			subPath, filepath.Join(PerfTaskDataPath, l.taskReq.GetTaskId(), l.taskReq.GetExecuteId(), PerfDataPath),
		)
	}

	if l.perfCase.GetPerfData() == nil {
		// from account
		totalLine, err = l.getPerfDataFromAccount(subPath)
		if err != nil {
			return errorzh.ErrZhAccountPullFailed(err)
		}

		return nil
	}

	if runtime.GOOS == "windows" {
		totalLine, err = l.splitPerfDataFile(
			filepath.Join(
				l.perfCase.GetPerfData().GetPath(),
				fmt.Sprintf("perf_data_id-%s", l.perfCase.GetPerfData().GetDataId()),
			), subPath,
		)
		if err != nil {
			return err
		}

		return nil
	}

	totalLine, err = l.splitPerfDataFile(l.perfCase.GetPerfData().GetPath(), subPath)
	if err != nil {
		return err
	}

	return nil
}

// generatePerfCase 生成测试用例, 从源目录中copy到目标目录
func (l *ExecutePerfTestLogic) generatePerfCase() (err error) {
	targetPath := filepath.Join(PerfTaskDataPath, l.taskReq.GetTaskId(), l.taskReq.GetExecuteId(), PerfCasePath)
	targetFile := filepath.Join(targetPath, l.taskReq.GetPerfCase().GetPerfCaseId())
	defer func() {
		if err == nil {
			l.perfCasePath = targetFile
		}
	}()

	l.Infof(
		"[ExecutePerfTestLogic.generatePerfCase] originPath:%s , targetPath:%s", l.perfCase.GetPath(), targetPath,
	)

	if err = os.MkdirAll(targetPath, 0o755); err != nil {
		l.Errorf("[ExecutePerfTestLogic.generatePerfCase] mkdir all error: %+v", err)
		return err
	}

	err = commonutils.CopyFile(l.perfCase.GetPath(), targetFile)
	if err != nil {
		l.Errorf("[ExecutePerfTestLogic.generatePerfCase] copy file error: %+v", err)
		return err
	}

	return nil
}

func (l *ExecutePerfTestLogic) calculateTimeout() error {
	executeType := l.taskReq.GetPerfCase().GetPerfPlanInfo().GetExecuteType()

	switch executeType {
	case commonpb.PerfTaskType_RUN:
		l.timeout = l.taskInfo.GetDuration() + defaultPerfTaskTimeout
	case commonpb.PerfTaskType_DEBUG:
		options := make([]calculate.Option, 0, 2)
		switch l.taskInfo.GetProtocol() {
		case commonpb.Protocol_PROTOCOL_TT:
			options = append(
				options, calculate.WithAuthRateLimits(l.taskInfo.GetRateLimits().GetAuth().GetRateLimits()),
			)
		case commonpb.Protocol_PROTOCOL_TT_AUTH:
			options = append(
				options, calculate.WithGlobalRateLimits(l.taskInfo.GetRateLimits().GetAuth().GetRateLimits()),
			)
		default:
			options = append(options, calculate.WithoutAuth())
		}
		times := l.taskInfo.GetTimes()
		if times == 0 {
			times = 1
		}
		options = append(options, calculate.WithExecutionTimes(times))
		durations := calculate.CalculateTotalCaseDuration(
			&commonpb.PerfCaseContentV2{
				SetupSteps:    l.perfCase.GetSetupSteps(),
				SerialSteps:   l.perfCase.GetSerialSteps(),
				ParallelSteps: l.perfCase.GetParallelSteps(),
				TeardownSteps: l.perfCase.GetTeardownSteps(),
			}, l.totalOfVU, options...,
		)
		l.Infof("durations of calculation by perfworker: %s", durations)

		l.timeout = uint32(durations.TotalDuration.Seconds()) + defaultPerfTaskTimeout
	default:
		return errors.Errorf(
			"invalid execute type, task_id: %s, execute_id: %s, project_id: %s, execute_type: %s",
			l.taskInfo.GetTaskId(), l.taskInfo.GetExecuteId(), l.taskInfo.GetProjectId(), executeType.String(),
		)
	}

	l.Infof("timeout of perf task, execute_type: %s, timeout: %ds", executeType, l.timeout)
	return nil
}

func (l *ExecutePerfTestLogic) splitPerfDataFile(originFile string, subPath []string) (int, error) {
	// 打开大文件
	inputFile, err := os.Open(originFile)
	if err != nil {
		l.Errorf("[ExecutePerfTestLogic.splitPerfDataFile] open origin path file error:%s", err.Error())
		return 0, err
	}
	defer func() {
		_ = inputFile.Close()
	}()

	// 创建小文件和缓冲写入器
	files := make([]*os.File, 0, len(subPath))
	defer func() {
		for _, file := range files {
			if file != nil {
				_ = file.Close()
			}
		}
	}()
	for i, path := range subPath {
		if err = os.MkdirAll(path, 0o755); err != nil {
			l.Errorf("[ExecutePerfTestLogic.splitPerfDataFile] mkdir all error:%s", err.Error())
			return 0, err
		}
		subPath[i] = filepath.Join(path, fmt.Sprintf("data-%d.csv", i))

		outputFile, err := os.Create(subPath[i])
		if err != nil {
			l.Errorf("[ExecutePerfTestLogic.splitPerfDataFile] create file error:%s", err.Error())
			return 0, err
		}

		files = append(files, outputFile)
	}

	// 读取大文件的内容
	var (
		scanner = bufio.NewScanner(inputFile)

		title string
	)

	// 读取标题行
	if scanner.Scan() {
		title = scanner.Text()
	} else if err = scanner.Err(); err != nil { // 添加错误处理
		l.Errorf("[ExecutePerfTestLogic.splitPerfDataFile] read title error:%s", err.Error())
		return 0, err
	}

	// 逐行读取数据并写入小文件
	var (
		lines   = 0
		amount  = len(files)
		writers = make([]*bufio.Writer, amount)
	)

	for scanner.Scan() {
		index := lines % amount
		if writers[index] == nil {
			writers[index] = bufio.NewWriter(files[index])

			// 写入标题行到每个小文件
			_, err = writers[index].WriteString(title + "\n")
			if err != nil {
				l.Errorf("[ExecutePerfTestLogic.splitPerfDataFile] write title error: %s", err.Error())
				return 0, err
			}
		}

		// 计算当前行应该写入哪个小文件
		_, err = writers[index].WriteString(scanner.Text() + "\n")
		if err != nil {
			l.Errorf("[ExecutePerfTestLogic.splitPerfDataFile] write data error: %s", err.Error())
			return 0, err
		}

		lines++
	}

	// 检查扫描错误
	if err = scanner.Err(); err != nil {
		l.Errorf("[ExecutePerfTestLogic.splitPerfDataFile] scanner err error: %s", err.Error())
	}

	// 刷新所有缓冲写入器
	for _, writer := range writers {
		_ = writer.Flush()
	}

	return lines, nil
}

func (l *ExecutePerfTestLogic) generateBizJob(jobName string) (job *batchv1.Job) {
	var (
		numberOfLG              = int32(l.perfCase.GetLoadGenerator().GetNumberOfLg())
		backoffLimit            = int32(0)
		ttlSecondsAfterFinished = int32(l.timeout)

		activeDeadlineSeconds *int64
	)

	job = &batchv1.Job{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Job",
			APIVersion: "batch/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: l.svcCtx.Config.RunEnv.Namespace,
			Labels:    map[string]string{},
		},
		Spec: batchv1.JobSpec{
			Parallelism:           &numberOfLG,
			Completions:           &numberOfLG,
			ActiveDeadlineSeconds: activeDeadlineSeconds,
			BackoffLimit:          &backoffLimit,
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					// 不注入sidecar
					Annotations: map[string]string{
						"sidecar.istio.io/inject":                        "false",
						"telemetry.mesh.quwan.io/customMetricsContainer": "perftool",
						"telemetry.mesh.quwan.io/customMetricsPath":      "/metrics",
						"telemetry.mesh.quwan.io/customMetricsPort":      "9101",
						"telemetry.mesh.quwan.io/customMetricsScrape":    "true",
					},
					Labels: map[string]string{
						"app":        "perftool",
						"execute-id": util.GenerateLabelKey(l.taskReq.GetExecuteId()),
						"task-id":    util.GenerateLabelKey(l.taskReq.GetTaskId()),
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name:    "perftool",
							Image:   l.svcCtx.Config.PerfToolConf.Image,
							Command: []string{"/app/perftool"},
							Args:    []string{"--mqc-config", "/app/etc/perftool.yaml"},
							Env:     l.generateEnv(),
							Resources: corev1.ResourceRequirements{
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse(l.perfCase.GetLoadGenerator().GetLimitsOfCpu()),
									corev1.ResourceMemory: resource.MustParse(l.perfCase.GetLoadGenerator().GetLimitsOfMemory()),
								},
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse(l.perfCase.GetLoadGenerator().GetRequestsOfCpu()),
									corev1.ResourceMemory: resource.MustParse(l.perfCase.GetLoadGenerator().GetRequestsOfMemory()),
								},
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      volumeName,
									MountPath: l.svcCtx.Config.PerfToolConf.MountPath,
								},
							},
							ImagePullPolicy: corev1.PullAlways,
							ReadinessProbe: &corev1.Probe{
								FailureThreshold:    3,
								InitialDelaySeconds: 10,
								PeriodSeconds:       10,
								SuccessThreshold:    1,
								TimeoutSeconds:      1,
								ProbeHandler: corev1.ProbeHandler{
									HTTPGet: &corev1.HTTPGetAction{
										Path:   "/healthz",
										Port:   intstr.FromInt32(9101),
										Scheme: corev1.URISchemeHTTP,
									},
								},
							},
						},
					},
					RestartPolicy: corev1.RestartPolicyNever,
					SecurityContext: &corev1.PodSecurityContext{
						Sysctls: []corev1.Sysctl{
							{
								Name:  "net.ipv4.ip_local_port_range",
								Value: "2048 64000",
							},
							//{
							//	Name:  "net.ipv4.tcp_keepalive_time",
							//	Value: "120",
							//},
							//{
							//	Name:  "net.core.somaxconn",
							//	Value: "60000",
							//},

							//{
							//	Name:  "net.ipv4.tcp_tw_recycle",
							//	Value: "1",
							//},
							//{
							//	Name:  "net.ipv4.tcp_tw_reuse",
							//	Value: "1",
							//},
						},
					},
				},
			},
			TTLSecondsAfterFinished: &ttlSecondsAfterFinished,
		},
	}
	if l.svcCtx.Config.RunEnv.IsServerless {
		_ = l.injectSuperNode(job)
	}

	l.injectTolerations(job)

	l.injectVolume(job)

	return job
}

func (l *ExecutePerfTestLogic) generateEnv() (envs []corev1.EnvVar) {
	envs = make([]corev1.EnvVar, 0)

	loggerLevel := l.svcCtx.Config.PerfToolConf.LogLevel
	if l.taskReq.GetPerfCase().GetPerfPlanInfo().GetExecuteType() == commonpb.PerfTaskType_DEBUG {
		loggerLevel = "debug"
	}

	envs = append(
		envs, corev1.EnvVar{
			Name:  "LOG_LEVEL",
			Value: loggerLevel,
		}, corev1.EnvVar{
			Name:  "REDIS_HOST",
			Value: l.svcCtx.Config.PerfToolConf.RedisHost,
		},
		corev1.EnvVar{
			Name:  "REDIS_TYPE",
			Value: l.svcCtx.Config.PerfToolConf.RedisType,
		},
		corev1.EnvVar{
			Name:  "REDIS_PASS",
			Value: l.svcCtx.Config.PerfToolConf.RedisPass,
		},
		corev1.EnvVar{
			Name:  "REDIS_DB",
			Value: strconv.FormatInt(int64(l.svcCtx.Config.PerfToolConf.RedisDb), 10),
		},
		corev1.EnvVar{
			Name:  "CONSUMER_BROKER",
			Value: l.svcCtx.Config.PerfToolConf.ConsumerBroker,
		},
		corev1.EnvVar{
			Name:  "CONSUMER_BACKEND",
			Value: l.svcCtx.Config.PerfToolConf.ConsumerBackend,
		},
		corev1.EnvVar{
			Name:  "CONSUMER_QUEUE",
			Value: l.queue,
		},
		corev1.EnvVar{
			Name:  "CONSUMER_DB",
			Value: strconv.FormatInt(int64(l.svcCtx.Config.PerfToolConf.ConsumerDb), 10),
		},
		corev1.EnvVar{
			Name:  "PERFWORKER_PRODUCER_BROKER",
			Value: l.svcCtx.Config.PerfWorkerConsumer.Broker,
		},
		corev1.EnvVar{
			Name:  "PERFWORKER_PRODUCER_BACKEND",
			Value: l.svcCtx.Config.PerfWorkerConsumer.Backend,
		},
		corev1.EnvVar{
			Name:  "PERFWORKER_PRODUCER_QUEUE",
			Value: l.svcCtx.Config.PerfWorkerConsumer.Queue,
		},
		corev1.EnvVar{
			Name:  "PERFWORKER_PRODUCER_DB",
			Value: strconv.FormatInt(int64(l.svcCtx.Config.PerfWorkerConsumer.Db), 10),
		},
		corev1.EnvVar{
			Name:  "REPORTER_TARGET",
			Value: l.svcCtx.Config.PerfToolConf.ReporterTarget,
		},
		corev1.EnvVar{
			Name:  "TASK_ID",
			Value: l.taskReq.GetTaskId(),
		},
		corev1.EnvVar{
			Name:  "EXECUTE_ID",
			Value: l.taskReq.GetPerfCase().GetPerfCaseExecuteId(),
		},
		corev1.EnvVar{
			Name:  "TASK_TYPE_NAME",
			Value: l.taskName,
		},
	)

	return envs
}

type mrGenerateItem struct {
	pod   *corev1.Pod
	index int
}

func (l *ExecutePerfTestLogic) sendTaskInfoToStartedPod(jobName string) error {
	var (
		taskID    = l.taskInfo.GetTaskId()
		executeID = l.taskInfo.GetExecuteId()
		count     = len(l.perfDataPaths)
	)

	timer := timewheel.NewTimer(common.MaxTimeoutOfWaitJobStarted)
	defer timer.Stop()

	ticker := timewheel.NewTicker(common.IntervalOfCheckJobStarted)
	defer ticker.Stop()

	defer func() {
		task := base.NewTask(
			constants.MQTaskTypePerfWorkerRemovePerfTestQueue,
			[]byte(l.queue),
			base.WithQueueOptions(l.svcCtx.Config.PerfWorkerConsumer.Queue),
		)
		if _, err := l.svcCtx.PerfWorkerProducer.SendDelay(
			context.Background(), task, 3*time.Hour, base.QueuePriorityDefault,
		); err != nil {
			l.Errorf(
				"failed to send the task to mq, task_id: %s, execute_id: %s, task_name: %s, payload: %s, error: %+v",
				taskID, executeID, task.Typename, task.Payload, err,
			)
		}
	}()

	// [2025-07-05] 改为全部pod启动后再一起发送任务
	for {
		select {
		case <-l.ctx.Done():
			err := l.ctx.Err()
			l.Warnf(
				"got a context done signal while sending tasks to started pods, task_id: %s, execute_id: %s, job: %s, error: %+v",
				taskID, executeID, jobName, err,
			)
			return err
		case <-l.stopCh:
			l.Warnf(
				"got a stop signal while sending tasks to started pods, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, jobName,
			)
			return errors.Errorf(
				"got a stop signal while sending tasks to started pods, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, jobName,
			)
		case <-timer.C:
			l.Errorf(
				"timeout while waiting for the tasks be sent to started pods, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, jobName,
			)
			return errors.Errorf(
				"timeout while waiting for the tasks be sent to started pods, task_id: %s, execute_id: %s, job: %s",
				taskID, executeID, jobName,
			)
		case <-ticker.C:
			pods, err := l.ListPodWithJob(jobName)
			if err != nil {
				return err
			}

			started := 0
			for _, pod := range pods {
				if pod.Status.Phase != corev1.PodPending {
					started++
				}
			}

			if started >= count {
				sent := 0
				_ = mr.MapReduceVoid[*mrGenerateItem, any](
					func(source chan<- *mrGenerateItem) {
						index := 0
						for _, pod := range pods {
							if pod.Status.Phase != corev1.PodRunning {
								l.Warnf(
									"got a not running pod, task_id: %s, execute_id: %s, job: %s, pod: %s, status: %s, reason: %s",
									taskID, executeID, jobName, pod.Name, pod.Status.Phase, pod.Status.Reason,
								)
								continue
							} else if index >= count {
								l.Warnf(
									"no data to send to started pod, task_id: %s, execute_id: %s, job: %s, pod: %s, status: %s",
									taskID, executeID, jobName, pod.Name, pod.Status.Phase,
								)
								continue
							}

							source <- &mrGenerateItem{
								pod:   pod,
								index: index,
							}
							index++
						}
					},
					func(item *mrGenerateItem, writer mr.Writer[any], cancel func(error)) {
						if item == nil {
							return
						}

						if err = l.sendTaskInfoToPerfTool(l.perfDataPaths[item.index]); err != nil {
							l.Errorf(
								"failed to send the task to started pod, task_id: %s, execute_id: %s, job: %s, pod: %s, index: %d, error: %+v",
								taskID, executeID, jobName, item.pod.Name, item.index, err,
							)
						} else {
							l.Infof(
								"send the task to started pod successfully, task_id: %s, execute_id: %s, job: %s, pod: %s, index: %d",
								taskID, executeID, jobName, item.pod.Name, item.index,
							)
							sent++
						}
					},
					func(pipe <-chan any, cancel func(error)) {
					},
					mr.WithContext(l.ctx),
					mr.WithWorkers(count),
				)

				l.Infof(
					"finished to send tasks to started pods, task_id: %s, execute_id: %s, job: %s, sent: %d, started: %d",
					taskID, executeID, jobName, sent, started,
				)
				return nil
			}
		}
	}
}

func (l *ExecutePerfTestLogic) sendTaskInfoToPerfTool(perfDataPath string) error {
	var (
		projectID   = l.taskInfo.GetProjectId()
		taskID      = l.taskInfo.GetTaskId()
		executeID   = l.taskInfo.GetExecuteId()
		executeType = l.taskReq.GetPerfCase().GetPerfPlanInfo().GetExecuteType()

		mode  commonpb.PerfTaskExecutionMode
		times uint32
	)

	switch executeType {
	case commonpb.PerfTaskType_RUN:
		mode = commonpb.PerfTaskExecutionMode_BY_DURATION
	case commonpb.PerfTaskType_DEBUG:
		mode = commonpb.PerfTaskExecutionMode_BY_TIMES
		times = 1
	default:
		return errors.Errorf(
			"invalid execute type, task_id: %s, execute_id: %s, project_id: %s, execute_type: %s",
			taskID, executeID, projectID, executeType.String(),
		)
	}

	data := &commonpb.PerfTestTaskInfo{
		ProjectId:      projectID,
		TaskId:         taskID,
		ExecuteId:      executeID,
		SuiteExecuteId: l.taskReq.GetPerfCase().GetPerfSuiteExecuteId(),
		PlanExecuteId:  l.taskReq.GetPerfCase().GetPerfPlanExecuteId(),
		TriggerMode:    l.taskInfo.GetTriggerMode(),

		ExecuteMode:     mode,
		Protocol:        l.taskInfo.GetProtocol(),
		TargetEnv:       l.taskInfo.GetTargetEnv(),
		ProtobufTargets: l.protobufTargets,
		GeneralConfig:   l.taskInfo.GetGeneralConfig(),
		Keepalive:       l.taskInfo.GetKeepalive(),
		PerfCasePath:    l.perfCasePath,
		PerfDataPath:    perfDataPath,
		Duration:        l.taskInfo.GetDuration(),
		Times:           times,
		PerfCase: &commonpb.PerfCaseContentV2{
			SetupSteps:    l.perfCase.GetSetupSteps(),
			SerialSteps:   l.perfCase.GetSerialSteps(),
			ParallelSteps: l.perfCase.GetParallelSteps(),
			TeardownSteps: l.perfCase.GetTeardownSteps(),
		},
		RateLimits: l.taskInfo.GetRateLimits(),

		Timeout:   l.timeout,
		TotalOfVu: l.totalOfVU,
		TotalOfLg: l.perfCase.GetLoadGenerator().GetNumberOfLg(),
	}
	pbData, err := protobuf.MarshalJSON(data)
	if err != nil {
		return errors.Errorf("failed to marshal the protobuf message in JSON format, data: %+v, error: %+v", data, err)
	}

	task := base.NewTask(
		l.taskName, pbData,
		base.WithQueueOptions(l.queue),
		base.WithMaxRetryOptions(0),
		base.WithTimeoutOptions(2*time.Hour),
	)
	_, err = l.svcCtx.PerfWorkerProducer.SendDelay(
		l.ctx, task, sendTaskDelayTime, base.QueuePriorityDefault,
	)
	if err != nil {
		return errors.Errorf(
			"failed to send the task to mq, task_id: %s, execute_id: %s, queue: %s, task_name: %s, payload: %s, error: %+v",
			taskID, executeID, task.Queue, task.Typename, task.Payload, err,
		)
	}

	l.Infof(
		"send the task to mq, task_id: %s, execute_id: %s, queue: %s, task_name: %s, payload: %s",
		taskID, executeID, task.Queue, task.Typename, task.Payload,
	)
	return nil
}

// getPerfDataFromAccount 从account服务分批获取账号
func (l *ExecutePerfTestLogic) getPerfDataFromAccount(subPath []string) (int, error) {
	accounts, err := l.queryTestAccounts()
	if err != nil {
		return 0, err
	}

	err = l.writeToPerfDataFiles(subPath, accounts)
	if err != nil {
		return 0, err
	}

	return len(accounts), nil
}

// queryTestAccounts 占用测试账号
func (l *ExecutePerfTestLogic) queryTestAccounts() (
	accounts []*accountpb.QueryAccountPoolEnvDataResponse_Account, err error,
) {
	poolEnvTable := ""
	for _, config := range l.taskReq.GetAccountConfig() {
		if config.GetPoolEnvTable() != "" {
			poolEnvTable = config.GetPoolEnvTable()
			break
		}
	}
	if poolEnvTable == "" {
		return accounts, errors.New("not found account pool env table")
	}

	numberOfVu := l.perfCase.GetNumberOfVu()
	accounts = make([]*accountpb.QueryAccountPoolEnvDataResponse_Account, 0, numberOfVu)
	releaseAccounts := make([][]*accountpb.ReleaseTestAccountRequest_Account, 0, numberOfVu/queryAccountChunkSize+1)
	getAccountsFunc := func(index, expected int64) error {
		var (
			data *accountpb.QueryAccountPoolEnvDataResponse
			err  error
		)

		err = caller.RetryWithOptionDo(
			func() error {
				data, err = l.svcCtx.AccountRPC.QueryAccountPoolEnvData(
					l.ctx, &accountpb.QueryAccountPoolEnvDataRequest{
						PoolEnvTable:          poolEnvTable,
						SelectedColumnIdArray: QueryAccountPoolColumns,
						ExpectedCount:         expected,
					},
				)
				return err
			}, caller.WithInterval(5*time.Second),
		)
		if err != nil {
			l.Errorf("failed to call api of `account` service, api: QueryAccountPoolEnvData, error: %+v", err)
			return err
		}

		for _, account := range data.GetMatchData() {
			accounts = append(accounts, account)

			for _, column := range account.GetAccount() {
				if column.GetField() == queryAccountKeyOfAccount {
					releaseAccounts[index] = append(
						releaseAccounts[index], &accountpb.ReleaseTestAccountRequest_Account{
							Account:   column.GetValue(),
							LockValue: column.GetLockValue(),
						},
					)
					break
				}
			}
		}

		return nil
	}

	times := int64(0)
	count := int64(numberOfVu)
	for count >= queryAccountChunkSize {
		releaseAccounts = append(
			releaseAccounts, make([]*accountpb.ReleaseTestAccountRequest_Account, 0, queryAccountChunkSize),
		)
		if err := getAccountsFunc(times, queryAccountChunkSize); err != nil {
			return accounts, errors.Errorf(
				"failed to get the account data from `account` service, error: %+v", err,
			)
		}

		times += 1
		count -= queryAccountChunkSize
	}
	if count > 0 {
		releaseAccounts = append(
			releaseAccounts, make([]*accountpb.ReleaseTestAccountRequest_Account, 0, queryAccountChunkSize),
		)
		if err := getAccountsFunc(times, count); err != nil {
			return accounts, errors.Errorf(
				"failed to get the account data from `account` service, error: %+v", err,
			)
		}
	}

	if len(accounts) == 0 {
		return accounts, errorzh.ErrZhAccountUnavailable(errors.New("[ExecutePerfTestLogic.getPerfDataFromAccount] not found any accounts"))
	}

	for _, releaseAccount := range releaseAccounts {
		l.releasePoolAccount = append(
			l.releasePoolAccount, &accountpb.ReleaseTestAccountRequest_PoolAccount{
				PoolEnvTable: poolEnvTable,
				AccountArray: releaseAccount,
			},
		)
	}

	return accounts, nil
}

func (l *ExecutePerfTestLogic) writeToPerfDataFiles(
	subPath []string, accounts []*accountpb.QueryAccountPoolEnvDataResponse_Account,
) error {
	// 创建小文件和缓冲写入器
	files := make([]*os.File, 0, len(subPath))
	defer func() {
		for _, file := range files {
			if file != nil {
				_ = file.Close()
			}
		}
	}()
	for i, path := range subPath {
		if err := os.MkdirAll(path, 0o755); err != nil {
			l.Errorf("failed to create the directory, path: %s error: %+v", path, err)
			return err
		}

		subPath[i] = filepath.Join(path, fmt.Sprintf("data-%d.csv", i))
		outputFile, err := os.Create(subPath[i])
		if err != nil {
			l.Errorf("failed to create the file, file: %s, error: %+v", subPath[i], err)
			return err
		}

		files = append(files, outputFile)
	}

	var (
		amount  = len(files)
		writers = make([]*bufio.Writer, amount)
	)

	for i, account := range accounts {
		index := i % amount
		if writers[index] == nil {
			writers[index] = bufio.NewWriter(files[index])

			// 写入标题行到每个小文件
			_, err := writers[index].WriteString(perfDataTitle + "\n")
			if err != nil {
				l.Errorf("failed to write title to the perf data file, file: %s, error: %+v", files[index].Name(), err)
				return err
			}
		}

		_, err := writers[index].WriteString(generatePerfDataLine(account.GetAccount()) + "\n")
		if err != nil {
			l.Errorf("failed to write data to the perf data file, file: %s, error: %+v", files[index].Name(), err)
			return err
		}
	}

	// 刷新所有缓冲写入器
	for _, writer := range writers {
		if writer != nil {
			_ = writer.Flush()
		}
	}

	return nil
}

// releaseTestAccounts 释放测试账号
func (l *ExecutePerfTestLogic) releaseTestAccounts() error {
	l.Debugf("release test accounts, pool_count: %d", len(l.releasePoolAccount))
	if len(l.releasePoolAccount) == 0 {
		return nil
	}

	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.DefaultTimeoutOfReleaseAccounts)
	defer cancel()

	for _, item := range l.releasePoolAccount {
		if err := caller.RetryWithOptionDo(
			func() error {
				l.Debugf("release test account, pool_table: %s, account_count: %d", item.PoolEnvTable, len(item.GetAccountArray()))
				_, err := l.svcCtx.AccountProducer.Send(
					ctx, base.NewTask(
						constants.MQTaskTypeAccountReleaseTestAccount,
						protobuf.MarshalJSONIgnoreError(
							&accountpb.ReleaseTestAccountRequest{
								ReleaseTasAccountArray: []*accountpb.ReleaseTestAccountRequest_PoolAccount{item},
							},
						),
						base.WithMaxRetryOptions(0),
						base.WithRetentionOptions(5*time.Minute),
					), base.QueuePriorityDefault,
				)
				// _, err := l.svcCtx.AccountRPC.ReleaseTestAccount(
				// 	ctx, &accountpb.ReleaseTestAccountRequest{
				// 		ReleaseTasAccountArray: []*accountpb.ReleaseTestAccountRequest_PoolAccount{item},
				// 	},
				// )
				return err
			}, caller.WithInterval(500*time.Millisecond),
		); err != nil {
			l.Errorf("failed to call api of `account` service, api: ReleaseTestAccount, error: %+v", err)
			continue
		}
	}

	return nil
}

func generatePerfDataLine(columns []*accountpb.QueryAccountPoolEnvDataResponse_Column) string {
	authDataMap := make(map[string]any)
	requestData := make(map[string]any)
	for _, column := range columns {
		if column.Field == "account" {
			authDataMap["username"] = column.Value
			continue
		}
		if column.Field == "password" {
			authDataMap["password"] = column.Value
			continue
		}
		requestData[column.Field] = column.Value
	}
	authDataMapMarshal, _ := json.Marshal(authDataMap)
	requestDataMapMarshal, _ := json.Marshal([]map[string]any{requestData})

	return strings.Join([]string{string(authDataMapMarshal), string(requestDataMapMarshal)}, "|")
}

func splitIntoChunks(value, chunkSize int) []int {
	var chunks []int
	if value <= chunkSize {
		chunks = append(chunks, value)
		return chunks
	}

	// 计算块的数量
	for value > 0 {
		if value < chunkSize {
			chunks = append(chunks, value) // 添加最后一块
			break
		}
		chunks = append(chunks, chunkSize) // 添加完整块
		value -= chunkSize                 // 减去已处理的值
	}

	return chunks
}

func (l *ExecutePerfTestLogic) sendTaskResultToPerfPlanReporter() error {
	var (
		taskID   = l.taskReq.GetTaskId()
		perfCase = l.taskReq.GetPerfCase()
	)

	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.DefaultTimeoutOfInvokeRPC)
	defer cancel()

	l.state.CompareAndSwap(dispatcherpb.ComponentState_Started.String(), constants.PerfExecuteStateRunning.String())

	req := &reporterpb.ModifyPerfPlanRecordReq{
		TaskId:    taskID,
		ExecuteId: perfCase.GetPerfPlanExecuteId(),

		ProjectId: l.taskReq.GetProjectId(),
		PlanId:    perfCase.GetPerfPlanId(),

		Status:     l.state.String(),
		ApiMetrics: l.apiMetrics,
	}

	_, err := l.svcCtx.ReporterRPC.ModifyPlanRecord(ctx, req)
	if err != nil {
		return errors.Errorf(
			"failed to modify perf plan record, task_id: %s, execute_id: %s, error: %+v",
			taskID, perfCase.GetPerfCaseExecuteId(), err,
		)
	}

	return nil
}

func (l *ExecutePerfTestLogic) sendStartedNotification() {
	var (
		taskID    = l.taskReq.GetTaskId()
		executeID = l.taskReq.GetPerfCase().GetPerfPlanExecuteId()
		projectID = l.taskReq.GetProjectId()
		planID    = l.taskReq.GetPerfCase().GetPerfPlanId()

		executeType = l.taskReq.GetPerfCase().GetPerfPlanInfo().GetExecuteType()
	)

	if executeType != commonpb.PerfTaskType_RUN {
		l.Infof(
			"sending the started notification is only required when running the perf plan task, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, execute_type: %s",
			taskID, executeID, projectID, planID, executeType,
		)
		return
	}

	if _, err := l.svcCtx.DispatcherProducer.Send(
		l.ctx, base.NewTask(
			constants.MQTaskTypeDispatcherSendPerfNotification,
			protobuf.MarshalJSONIgnoreError(
				&dispatcherpb.PerfReportCallback{
					ProjectId:     projectID,
					TaskId:        taskID,
					PlanId:        planID,
					PlanExecuteId: executeID,
					Stage:         dispatcherpb.StageType_ST_STARTED,
				},
			),
			base.WithMaxRetryOptions(0),
			base.WithRetentionOptions(5*time.Minute),
		), base.QueuePriorityDefault,
	); err != nil {
		l.Errorf(
			"failed to send perf started notification task to dispatcher, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s, error: %+v",
			taskID, executeID, projectID, planID, err,
		)
	} else {
		l.Infof(
			"send perf started notification task to dispatcher successfully, task_id: %s, execute_id: %s, project_id: %s, plan_id: %s",
			taskID, executeID, projectID, planID,
		)
	}
}

func (l *ExecutePerfTestLogic) sendTaskResultToPerfCaseReporter() error {
	var (
		taskID   = l.taskReq.GetTaskId()
		perfCase = l.taskReq.GetPerfCase()
	)

	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.DefaultTimeoutOfInvokeRPC)
	defer cancel()

	req := &reporterpb.ModifyPerfCaseRecordReq{
		TaskId:         taskID,
		ExecuteId:      perfCase.GetPerfCaseExecuteId(),
		SuiteExecuteId: perfCase.GetPerfSuiteExecuteId(),

		ProjectId: l.taskReq.GetProjectId(),
		CaseId:    perfCase.GetPerfCaseId(),

		Status:     l.state.String(),
		EndedAt:    timestamppb.New(time.Now()),
		ApiMetrics: l.apiMetrics,
		ErrMsg:     l.errMsg,
	}

	_, err := l.svcCtx.ReporterRPC.ModifyCaseRecord(ctx, req)
	if err != nil {
		return errors.Errorf(
			"failed to modify perf case record, task_id: %s, execute_id: %s, error: %+v",
			taskID, perfCase.GetPerfCaseExecuteId(), err,
		)
	}

	return nil
}

func (l *ExecutePerfTestLogic) sendTaskCallbackDataToDispatcher() error {
	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.DefaultTimeoutOfSendTask)
	defer cancel()

	data := l.callbackData()
	task := base.NewTask(
		constants.MQTaskTypeDispatcherCallback, data,
		base.WithMaxRetryOptions(0),
	)
	if _, err := l.svcCtx.DispatcherProducer.Send(ctx, task, base.QueuePriorityDefault); err != nil {
		return errors.Errorf(
			"failed to send the task to mq, task_name: %s, payload: %s, error: %+v",
			task.Typename, task.Payload, err,
		)
	}

	return nil
}

func (l *ExecutePerfTestLogic) callbackData() []byte {
	var caseState dispatcherpb.ComponentState
	state, ok := dispatcherpb.ComponentState_value[l.state.String()]
	if !ok {
		caseState = dispatcherpb.ComponentState_Invalid
	} else {
		caseState = dispatcherpb.ComponentState(state)
	}

	perfCase := l.taskInfo.GetPerfCase()
	data, _ := protobuf.MarshalJSON(
		&dispatcherpb.CallbackReq{
			TriggerMode:  l.taskInfo.GetTriggerMode(),
			TriggerRule:  l.taskInfo.GetTriggerRule(),
			ProjectId:    l.taskInfo.GetProjectId(),
			TaskId:       l.taskInfo.GetTaskId(),
			ExecuteType:  l.taskInfo.GetExecuteType(),
			CallbackType: l.taskInfo.GetCallbackType(),
			Data: &dispatcherpb.CallbackReq_PerfCase{
				PerfCase: &dispatcherpb.PerfCaseCallbackData{
					PerfPlanId:         perfCase.GetPerfPlanId(),
					PerfPlanExecuteId:  perfCase.GetPerfPlanExecuteId(),
					PerfSuiteId:        perfCase.GetPerfSuiteId(),
					PerfSuiteExecuteId: perfCase.GetPerfSuiteExecuteId(),
					PerfCaseId:         perfCase.GetPerfCaseId(),
					PerfCaseExecuteId:  perfCase.GetPerfCaseExecuteId(),
					CaseState:          caseState,
					MetaData:           perfCase.GetMetaData(),
					// Metrics
				},
			},
		},
	)

	return data
}

// 识别集群, 调整注入方式
func (l *ExecutePerfTestLogic) injectSuperNode(job *batchv1.Job) error { //nolint: unparam
	// check env
	if l.svcCtx.Config.RunEnv.Env != "prod" {
		return nil
	}

	if strings.Contains(l.svcCtx.Config.RunEnv.Cluster, "tc") {
		job.Spec.Template.Spec.Tolerations = []corev1.Toleration{
			{
				Key:      "pool-type",
				Operator: corev1.TolerationOpEqual,
				Value:    "gpu",
				Effect:   corev1.TaintEffectNoSchedule,
			},
			{
				Key:      "biz-type",
				Operator: corev1.TolerationOpEqual,
				Value:    "offline",
				Effect:   corev1.TaintEffectNoSchedule,
			},
			{
				Key:      "eks.tke.cloud.tencent.com/eklet",
				Operator: corev1.TolerationOpExists,
				Effect:   corev1.TaintEffectNoSchedule,
			},
		}

		job.Spec.Template.Spec.Affinity = &corev1.Affinity{
			NodeAffinity: &corev1.NodeAffinity{
				PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{
					{
						Weight: 1,
						Preference: corev1.NodeSelectorTerm{
							MatchExpressions: []corev1.NodeSelectorRequirement{
								{
									Key:      "pool-type",
									Operator: corev1.NodeSelectorOpIn,
									Values:   []string{"super-node"},
								},
							},
						},
					},
				},
			},
		}
	}

	if strings.Contains(l.svcCtx.Config.RunEnv.Cluster, "hs") {
		if job.Spec.Template.Annotations == nil {
			job.Spec.Template.Annotations = make(map[string]string)
		}
		// https://www.volcengine.com/docs/6460/76920
		job.Spec.Template.Annotations["vke.volcengine.com/burst-to-vci"] = "enforce"
	}

	return nil
}

func (l *ExecutePerfTestLogic) injectTolerations(job *batchv1.Job) {
	// 火山集群 生产环境 没有使用serverless
	if l.svcCtx.Config.RunEnv.Env == "prod" && strings.Contains(
		l.svcCtx.Config.RunEnv.Cluster, "hs",
	) && !l.svcCtx.Config.RunEnv.IsServerless {
		if job.Spec.Template.Spec.Tolerations == nil {
			job.Spec.Template.Spec.Tolerations = make([]corev1.Toleration, 0)
			job.Spec.Template.Spec.NodeSelector = map[string]string{}
		}
		job.Spec.Template.Spec.Tolerations = append(
			job.Spec.Template.Spec.Tolerations, []corev1.Toleration{
				{
					Key:      "pool-type",
					Operator: corev1.TolerationOpEqual,
					Value:    "yace",
					Effect:   corev1.TaintEffectNoSchedule,
				},
			}...,
		)
		job.Spec.Template.Spec.NodeSelector["pool-type"] = "yace"
	}
}

func (l *ExecutePerfTestLogic) injectVolume(job *batchv1.Job) {
	volumeSource := corev1.VolumeSource{}

	// 腾讯云使用pvc方式
	if strings.Contains(l.svcCtx.Config.RunEnv.Cluster, "tc") {
		volumeSource = corev1.VolumeSource{
			PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
				ClaimName: l.svcCtx.Config.RunEnv.PvcName,
			},
		}
	}

	if l.svcCtx.Config.RunEnv.NfsServer != "" && l.svcCtx.Config.RunEnv.NfsPath != "" {
		// 火山云使用nfs方式
		if strings.Contains(l.svcCtx.Config.RunEnv.Cluster, "hs") {
			volumeSource = corev1.VolumeSource{
				NFS: &corev1.NFSVolumeSource{
					Server: l.svcCtx.Config.RunEnv.NfsServer,
					Path:   l.svcCtx.Config.RunEnv.NfsPath,
				},
			}
		}
	}

	job.Spec.Template.Spec.Volumes = []corev1.Volume{
		{
			Name:         volumeName,
			VolumeSource: volumeSource,
		},
	}
}

func (l *ExecutePerfTestLogic) ToolResultsSummary() (err error) {
	taskID := l.taskInfo.GetTaskId()
	executeID := l.taskInfo.GetExecuteId()
	key := fmt.Sprintf("%s::%s::%s", TaskResultsKeyPrefix, taskID, executeID)
	defer func() {
		_, _ = l.svcCtx.Redis.DelCtx(l.ctx, key)
	}()

	values := make(map[string]string)
	numberOfLG := int(l.perfCase.GetLoadGenerator().GetNumberOfLg())
	for i := 0; i < 10; i++ {
		time.Sleep(time.Duration(i) * time.Second)
		values, err = l.svcCtx.Redis.HgetallCtx(l.ctx, key)
		if err != nil {
			return err
		}
		if len(values) >= numberOfLG {
			break
		}
	}

	for filed, value := range values {
		l.Debugf("handle tool result, key: %s, field: %s, value: %s", key, filed, value)
		if len(value) == 0 {
			continue
		}

		var errMsg reporterpb.ErrorMessage
		if err := protobuf.UnmarshalJSONFromString(value, &errMsg); err != nil {
			return err
		}

		if len(errMsg.GetMessageZh()) > 0 {
			return errorzh.NewErrorZh(
				errors.New(errMsg.MessageEn),
				common.ErrMsgZh(errMsg.MessageZh),
			)
		}

		if len(errMsg.GetMessageEn()) > 0 {
			return errorzh.NewErrorZh(
				errors.New(errMsg.MessageEn),
				common.ErrMsgZh_Tool_OthersFailed,
			)
		}
	}

	return nil
}
