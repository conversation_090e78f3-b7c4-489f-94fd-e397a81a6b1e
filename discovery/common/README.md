# CreateTcpUsbBridge 功能说明

## 概述

`CreateTcpUsbBridge` 是一个用于将通过USB连接的Android设备切换到TCP/IP模式的功能，使设备可以通过网络连接进行调试和操作。

## 功能特性

- 🔌 **USB到TCP转换**: 将USB连接的Android设备切换到TCP/IP模式
- 🌐 **网络访问**: 支持通过IP地址和端口远程连接设备
- ⚙️ **灵活配置**: 支持自定义端口和超时时间
- 🛡️ **错误处理**: 完善的错误处理和验证机制
- 📱 **设备管理**: 支持指定设备序列号或自动选择第一个可用设备

## 基本用法

### 1. 简单使用（默认配置）

```go
package main

import (
    "fmt"
    "log"
    "gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/common"
)

func main() {
    // 使用默认端口5555和30秒超时
    address, err := common.CreateTcpUsbBridge("")
    if err != nil {
        log.Fatal(err)
    }
    fmt.Printf("设备现在可以通过 %s 连接\n", address)
}
```

### 2. 自定义配置

```go
// 指定端口和超时时间
address, err := common.CreateTcpUsbBridge("",
    common.WithPort(8080),
    common.WithTimeout(60*time.Second),
)
```

### 3. 指定设备

```go
// 为特定设备创建TCP桥接
address, err := common.CreateTcpUsbBridge("your_device_serial",
    common.WithPort(5556),
)
```

## 配置选项

### WithPort(port int)
设置TCP端口号
- **范围**: 1024-65535
- **默认值**: 5555
- **示例**: `WithPort(8080)`

### WithTimeout(timeout time.Duration)
设置连接超时时间
- **默认值**: 30秒
- **示例**: `WithTimeout(60*time.Second)`

## 使用场景

### 开发环境
快速将USB设备切换到无线调试模式：
```go
address, err := common.CreateTcpUsbBridge("")
// 现在可以拔掉USB线，通过WiFi连接设备
```

### 测试环境
为多个设备分配不同端口：
```go
devices := []string{"device1", "device2", "device3"}
basePort := 5555
for i, serial := range devices {
    address, err := common.CreateTcpUsbBridge(serial, 
        common.WithPort(basePort+i))
    // 每个设备使用不同端口：5555, 5556, 5557
}
```

### CI/CD环境
自动化测试中的设备管理：
```go
address, err := common.CreateTcpUsbBridge("test_device",
    common.WithPort(5555),
    common.WithTimeout(120*time.Second), // 更长的超时时间
)
```

## 错误处理

### 常见错误类型

1. **端口验证错误**
```go
_, err := common.CreateTcpUsbBridge("", common.WithPort(1023))
// 错误: invalid port: 1023, port must be between 1024 and 65535
```

2. **设备未找到**
```go
_, err := common.CreateTcpUsbBridge("nonexistent_device")
// 错误: failed to find device by serial: nonexistent_device
```

3. **连接超时**
```go
_, err := common.CreateTcpUsbBridge("", common.WithTimeout(5*time.Second))
// 错误: timeout waiting for tcp connection
```

### 推荐的错误处理模式

```go
address, err := common.CreateTcpUsbBridge(serial, options...)
if err != nil {
    switch {
    case strings.Contains(err.Error(), "invalid port"):
        // 端口配置错误，使用默认端口重试
        log.Printf("端口配置错误，使用默认端口: %v", err)
    case strings.Contains(err.Error(), "no devices found"):
        // 未找到设备，等待设备连接
        log.Printf("未找到设备，请检查USB连接: %v", err)
    case strings.Contains(err.Error(), "timeout"):
        // 连接超时，可能需要增加超时时间
        log.Printf("连接超时，请检查设备状态: %v", err)
    default:
        log.Printf("未知错误: %v", err)
    }
    return
}
log.Printf("成功创建TCP桥接: %s", address)
```

## 工作原理

1. **设备检测**: 检查指定设备或获取第一个可用设备
2. **状态验证**: 验证设备是否可访问
3. **TCP模式切换**: 设置设备的TCP端口属性
4. **服务重启**: 重启ADB守护进程以启用TCP模式
5. **IP获取**: 获取设备的网络IP地址
6. **连接验证**: 验证TCP连接是否成功建立

## 注意事项

- 设备必须已通过USB连接并且处于可访问状态
- 设备必须启用了开发者选项和USB调试
- 设备必须连接到与主机相同的网络
- 某些设备可能需要额外的权限确认
- 建议在生产环境中使用更长的超时时间

## 测试

运行单元测试：
```bash
cd discovery/common
go test -v
```

运行特定测试：
```bash
go test -v -run TestCreateTcpUsbBridgeOptions
```

## 依赖

- `github.com/electricbubble/gadb`: ADB客户端库
- `github.com/pkg/errors`: 错误处理库
