Name: perfworker

Log:
  ServiceName: mqc.perfworker
  Encoding: plain
  Level: info
  Path: /app/logs/perfworker
  Stat: false

Prometheus:
  Host: 0.0.0.0
  Port: 9101
  Path: /metrics

Telemetry:
  Name: mqc.perfworker
  Endpoint: http://tt-yw-tracing-jaeger.ttyuyin.com:9511
  Sampler: 1.0
  Batcher: zipkin

DevServer:
  Enabled: true
  Port: 6470

Redis:
  Key: mqc.perfworker
  Host: ************:6379
  Type: node
  Pass:
  DB: 20

DispatcherRedis:
  Host: ************:6379
  Type: node
  Pass:
  DB: 4

Cache:
  - Host: ************:6379
    Pass:
    DB: 20

Account:
  Target: account.probe-test.svc.cluster.local:8080
  NonBlock: true
  Timeout: 20000

Dispatcher:
  Target: dispatcher.probe-test.svc.cluster.local:8080
  NonBlock: true
  Timeout: 20000

Reporter:
  Target: reporter.probe-test.svc.cluster.local:8080
  NonBlock: true
  Timeout: 10000

PerfWorkerConsumer:
  Broker: *************:6379
  Backend: *************:6379
  Queue: mqc:perfworker
  ConsumerTag: mqc:perfworker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 20
  MaxWorker: 16

PerfWorkerProducer:
  Broker: *************:6379
  Backend: *************:6379
  Queue: mqc:perftool
  Db: 20

DispatcherProducer:
  Broker: *************:6379
  Backend: *************:6379
  Queue: mqc:dispatcher
  Db: 20

RunEnv:
  Cluster: k8s-tc-bj-1-test
  Namespace: probe-test
  PvcName: pvc-pressure
  Env: test
  IsServerless: false
  NfsServer: 6c12d6f8-847f-4475-9ef7-c3314765f233.sfsturbo.hw.quwan.com
  NfsPath: /

PerfToolConf:
  LogLevel: info
  RedisHost: ************:6379
  RedisType: node
  RedisPass:
  RedisDb: 21
  ConsumerBroker: *************:6379
  ConsumerBackend: *************:6379
  ConsumerQueue: mqc:perftool
  ConsumerDb: 20
  Image: cr.ttyuyin.com/probe/perftool:v1.0.0
  MountPath: /app/data
  ReporterTarget: probe-test.ttyuyin.com:8000

GitLab:
  Token: ********************

Constack:
  Url: cloud.ttyuyin.com:8100
  Token: Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663

Monitor:
  BaseURL: https://yw-hw-bj-sre-monitor-api.ttyuyin.com

AppInsight:
  BaseURL: http://tt-telemetry-web.ttyuyin.com

StopRules:
  p95:
    Threshold: 1000 # 单位为ms
    Duration: 1m
  fail_ratio:
    Threshold: 0.10 # 单位为%
    Duration: 1m
