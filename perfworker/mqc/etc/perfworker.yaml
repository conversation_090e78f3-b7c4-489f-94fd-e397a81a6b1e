Name: perfworker

Log:
  ServiceName: mqc.perfworker
  Encoding: plain
  Level: info
  Path: /app/logs/perfworker
  Stat: false

#Prometheus:
#  Host: 0.0.0.0
#  Port: 9101
#  Path: /metrics
#
#Telemetry:
#  Name: mqc.perfworker
#  Endpoint: http://tt-yw-tracing-jaeger.ttyuyin.com:9511
#  Sampler: 1.0
#  Batcher: zipkin
#
#DevServer:
#  Enabled: true
#  Port: 6470

Redis:
  Key: mqc.perfworker
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 20

DispatcherRedis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 4

Cache:
  - Host: 127.0.0.1:6379
    Pass:
    DB: 20

Account:
  Endpoints:
    - 127.0.0.1:20111
  NonBlock: true
  Timeout: 0

Dispatcher:
  Endpoints:
    - 127.0.0.1:20311
  NonBlock: true
  Timeout: 0

Reporter:
  Endpoints:
    - 127.0.0.1:20511
  NonBlock: true
  Timeout: 0

PerfWorkerConsumer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:perfworker
  ConsumerTag: mqc:perfworker
  IsEnableMetricsExporter: false # 新增加 暂时不需要开启
  IsMonitorHttp: false # 新增加 选择性开启
  Db: 20
  MaxWorker: 0

PerfWorkerProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:perftool
  Db: 20

DispatcherProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:dispatcher
  Db: 20

AccountProducer:
  Broker: 127.0.0.1:6379
  Backend: 127.0.0.1:6379
  Queue: mqc:account
  Db: 20

RunEnv:
  Cluster: k8s-tc-bj-1-test
  Namespace: probe-test
  PvcName: pvc-pressure
  Env: test
  IsServerless: false
  NfsServer: f55aee3f-f866-4741-9b98-b52d1be85044.sfsturbo.hw.quwan.com
  NfsPath: /

PerfToolConf:
  LogLevel: info
  RedisHost: 127.0.0.1:6379
  RedisType: node
  RedisPass:
  RedisDb: 21
  ConsumerBroker: 127.0.0.1:6379
  ConsumerBackend: 127.0.0.1:6379
  ConsumerQueue: mqc:perftool
  ConsumerDb: 20
  Image: cr.ttyuyin.com/probe/perftool:v1.0.0
  MountPath: /app/data
  ReporterTarget: probe-test.ttyuyin.com:8000

GitLab:
  Token: ********************

Constack:
  Url: cloud.ttyuyin.com:8100
  Token: Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663

Monitor:
  BaseURL: https://yw-hw-bj-sre-monitor-api.ttyuyin.com

AppInsight:
  BaseURL: http://tt-telemetry-web.ttyuyin.com

StopRules:
  p95:
    Threshold: 1000 # 单位为ms
    Duration: 1m
  fail_ratio:
    Threshold: 0.10 # 单位为%
    Duration: 1m
