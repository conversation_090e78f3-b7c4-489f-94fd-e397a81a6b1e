package account

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/client/account"
)

type AccountRPC struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  account.Account
}

func NewAccountRPC(conf zrpc.RpcClientConf) *AccountRPC {
	cli := &AccountRPC{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli:  account.NewAccount(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}

	return cli
}

func (ar *AccountRPC) QueryAccountPoolEnvData(
	ctx context.Context, in *account.QueryAccountPoolEnvDataRequest,
) (*account.QueryAccountPoolEnvDataResponse, error) {
	return ar.Cli.QueryAccountPoolEnvData(ctx, in)
}

func (ar *AccountRPC) ReleaseTestAccount(
	ctx context.Context, in *account.ReleaseTestAccountRequest,
) (*account.ReleaseTestAccountResponse, error) {
	return ar.Cli.ReleaseTestAccount(ctx, in)
}
