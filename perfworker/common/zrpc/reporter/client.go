package reporter

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	client "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/perfreporter"
)

type PerfReporterRPC struct {
	conf zrpc.RpcClientConf

	client client.PerfReporter
}

func NewPerfReporterRPC(c zrpc.RpcClientConf) *PerfReporterRPC {
	return &PerfReporterRPC{
		conf:   c,
		client: client.NewPerfReporter(zrpc.MustNewClient(c, clientinterceptors.UnaryUserInfoClientOption())),
	}
}

func (c *PerfReporterRPC) CreateCaseRecord(
	ctx context.Context, req *client.CreatePerfCaseRecordReq, opts ...grpc.CallOption,
) (*client.CreatePerfCaseRecordResp, error) {
	return c.client.CreatePerfCaseRecord(ctx, req, opts...)
}

func (c *PerfReporterRPC) ModifyCaseRecord(
	ctx context.Context, req *client.ModifyPerfCaseRecordReq, opts ...grpc.CallOption,
) (*client.ModifyPerfCaseRecordResp, error) {
	return c.client.ModifyPerfCaseRecord(ctx, req, opts...)
}

func (c *PerfReporterRPC) ModifyPlanRecord(
	ctx context.Context, req *client.ModifyPerfPlanRecordReq, opts ...grpc.CallOption,
) (*client.ModifyPerfPlanRecordResp, error) {
	return c.client.ModifyPerfPlanRecord(ctx, req, opts...)
}

func (c *PerfReporterRPC) GetPerfPlanRecord(
	ctx context.Context, in *client.GetPerfPlanRecordReq, opts ...grpc.CallOption,
) (*client.GetPerfPlanRecordResp, error) {
	return c.client.GetPerfPlanRecord(ctx, in, opts...)
}
