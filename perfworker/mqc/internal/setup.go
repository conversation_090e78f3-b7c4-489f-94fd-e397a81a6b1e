package internal

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"

	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	commonconsts "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/tasks"
)

func SetupOperation(svcCtx *svc.ServiceContext, needToLaunch bool) error {
	// register tasks
	if err := registerTasks(svcCtx); err != nil {
		return errors.Wrap(err, "failed to register tasks")
	}
	if needToLaunch {
		// start consumer
		launchConsumer(svcCtx.PerfWorkerConsumer)
	}
	return nil
}

func registerTasks(svcCtx *svc.ServiceContext) error {
	err := svcCtx.PerfWorkerConsumer.RegisterHandlers(
		consumerv2.NewTaskHandlerOjb(
			commonconsts.MQTaskTypePerfWorkerExecutePerfTest, tasks.NewProcessorExecutePerfTest(svcCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			commonconsts.MQTaskTypePerfWorkerFinalHandle, tasks.NewProcessorFinalHandle(svcCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			commonconsts.MQTaskTypePerfWorkerRemovePerfTestQueue, tasks.NewProcessorRemovePerfTestQueue(svcCtx),
		),
		consumerv2.NewTaskHandlerOjb(
			commonconsts.MQTaskTypePerfWorkerToolResultFeedback, tasks.NewProcessorToolResultFeedback(svcCtx),
		),
	)
	if err != nil {
		return err
	}

	logx.Info("register task done!")
	return nil
}

func launchConsumer(consumer *consumerv2.Consumer) {
	threading.GoSafe(consumer.Start)

	proc.AddShutdownListener(
		func() {
			logx.Info("stopping the consumer")
			consumer.Stop()
		},
	)
}

func TeardownOperation(svcCtx *svc.ServiceContext) error {
	proc.AddShutdownListener(svcCtx.PerfWorkerConsumer.Stop)
	return nil
}
