package errorzh

import "gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common"

func ErrZhOthersFailed(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_OthersFailed)
}

func ErrZhPVCCheckFailed(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_PVCCheckFailed)
}

func ErrZhPVCNotFound(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_PVCNotFound)
}

func ErrZhProtoPullFailed(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_ProtoPullFailed)
}

func ErrZhPerfDataGenFailed(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_PerfDataGenFailed)
}

func ErrZhAccountPullFailed(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_AccountPullFailed)
}

func ErrZhAccountUnavailable(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_AccountUnavailable)
}

func ErrZhJobCreateFailed(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_JobCreateFailed)
}

func ErrZhPerfTaskSendFailed(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_PerfTaskSendFailed)
}

func ErrZhPerfResultReportFailed(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_PerfResultReportFailed)
}

func ErrZhJobWaitCompletedFailed(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_JobWaitCompletedFailed)
}

func ErrZhPerfWorkerShutdown(err error) ErrZh {
	return NewErrorZh(err, common.ErrMsgZh_Worker_PerfWorkerShutdown)
}
