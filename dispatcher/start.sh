#!/bin/sh

path="/app/dispatcher"
project="dispatcher"
cluster="dispatcher"

# api
mkdir -p ${path}/api/etc/
curl -X GET -s -H "Content-Type:application/json;charset=UTF-8" \
 -H "Authorization:${APOLLO_AUTH_TOKEN}" \
  http://${APOLLO_ADDR}/openapi/v1/envs/dev/apps/probe-backend/clusters/${cluster}/namespaces/api.yaml | jq .items[0].value | sed 's/^"\|"$//g' | sed ':a;:N;$!ba;s/\\n/\n/g' \
   > ${path}/api/etc/${project}.yaml

# rpc
mkdir -p ${path}/rpc/etc/
curl -X GET -s -H "Content-Type:application/json;charset=UTF-8" \
 -H "Authorization:${APOLLO_AUTH_TOKEN}" \
  http://${APOLLO_ADDR}/openapi/v1/envs/dev/apps/probe-backend/clusters/${cluster}/namespaces/rpc.yaml | jq .items[0].value | sed 's/^"\|"$//g' | sed ':a;:N;$!ba;s/\\n/\n/g' \
   > ${path}/rpc/etc/${project}.yaml

# mqc
mkdir -p ${path}/mqc/etc/
curl -X GET -s -H "Content-Type:application/json;charset=UTF-8" \
 -H "Authorization:${APOLLO_AUTH_TOKEN}" \
  http://${APOLLO_ADDR}/openapi/v1/envs/dev/apps/probe-backend/clusters/${cluster}/namespaces/mqc.yaml | jq .items[0].value | sed 's/^"\|"$//g' | sed ':a;:N;$!ba;s/\\n/\n/g' \
   > ${path}/mqc/etc/${project}.yaml

# beat
mkdir -p ${path}/beat/etc/
curl -X GET -s -H "Content-Type:application/json;charset=UTF-8" \
 -H "Authorization:${APOLLO_AUTH_TOKEN}" \
  http://${APOLLO_ADDR}/openapi/v1/envs/dev/apps/probe-backend/clusters/${cluster}/namespaces/beat.yaml | jq .items[0].value | sed 's/^"\|"$//g' | sed ':a;:N;$!ba;s/\\n/\n/g' \
   > ${path}/beat/etc/${project}.yaml

nohup ${path}/${project}.linux --api-config ${path}/api/etc/${project}.yaml --rpc-config ${path}/rpc/etc/${project}.yaml --mqc-config ${path}/mqc/etc/${project}.yaml --beat-config ${path}/beat/etc/${project}.yaml >> /app/logs/${project}/stdout.log 2>&1 &
