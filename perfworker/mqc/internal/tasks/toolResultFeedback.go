package tasks

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/logic/perftest"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

type ProcessorToolResultFeedback struct {
	svcCtx *svc.ServiceContext
}

func NewProcessorToolResultFeedback(svcCtx *svc.ServiceContext) (call base.Handler) {
	return &ProcessorToolResultFeedback{
		svcCtx: svcCtx,
	}
}

func (processor *ProcessorToolResultFeedback) ProcessTask(ctx context.Context, task *base.Task) (result []byte, err error) {
	logger := logx.WithContext(ctx)
	logger.Infof("received the perf_tool_result_feedback task, playload: %s", task.Payload)

	var req types.ResultFeedbackTaskInfo
	if err = jsonx.Unmarshal(task.Payload, &req); err != nil {
		return []byte(constants.FAILURE), errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()),
			"failed to unmarshal the payload of perf_tool_result_feedback task, payload: %s, error: %+v", task.Payload, err,
		)
	}

	key := fmt.Sprintf("%s::%s::%s", perftest.TaskResultsKeyPrefix, req.TaskId, req.ExecuteId)
	filed := req.PodName
	value := protobuf.MarshalJSONToStringIgnoreError(req.ErrMsg)
	if err = processor.svcCtx.Redis.HsetCtx(ctx, key, filed, value); err != nil {
		return []byte(constants.FAILURE), errors.Wrapf(
			errorx.Err(errorx.RedisError, err.Error()),
			"failed to hset the task results, key: %s, field: %s, value: %s, error: %+v", key, filed, value, err,
		)
	}

	return []byte(constants.SUCCESS), nil
}
