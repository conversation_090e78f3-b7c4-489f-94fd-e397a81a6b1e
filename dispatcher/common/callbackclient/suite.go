package callbackclient

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

func CallBack(
	ctx context.Context, cli *producer.Producer, serverName, queue, uuid string, cb *pb.CallbackReq,
) (err error) {
	pbData, err := protobuf.MarshalJSON(cb)
	if err != nil {
		return fmt.Errorf("failed to marshal the callback data, data: %+v, err: %+v", cb, err)
	}
	logx.Infof("callback task data: %s", pbData)
	/*task_sign := &tasks.Signature{
		UUID:       uuid,
		RoutingKey: queue,
		Name:       constants.MQTaskTypeDispatcherCallback,
		Args: []tasks.Arg{
			{Value: pbData, Type: "[]byte"},
		},
	}

	_, err = cli.AsyncPush(ctx, task_sign, serverName)
	if err != nil {
		logx.Infof("AsyncPush err: %s", err)
		return fmt.Errorf("发送callback失败: %s", err)
	}*/

	task := base.NewTask(
		constants.MQTaskTypeDispatcherCallback,
		pbData,
		base.WithRetentionOptions(time.Minute*2),
		base.WithMaxRetryOptions(3),
	)
	_, err = cli.Send(ctx, task, base.QueuePriorityDefault)
	if err != nil {
		return fmt.Errorf(
			"failed to send the task to mq, task_name: %s, payload: %s, error: %+v",
			task.Typename, task.Payload, err,
		)
	}

	return nil
}
