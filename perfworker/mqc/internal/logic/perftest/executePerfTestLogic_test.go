package perftest

import (
	"reflect"
	"testing"
)

func Test_splitIntoChunks(t *testing.T) {
	type args struct {
		value     int
		chunkSize int
	}
	tests := []struct {
		name string
		args args
		want []int
	}{
		{
			name: "1250",
			args: args{
				value:     1050,
				chunkSize: 500,
			},
			want: []int{500, 500, 50},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := splitIntoChunks(tt.args.value, tt.args.chunkSize); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("splitIntoChunks() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
