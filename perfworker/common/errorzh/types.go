package errorzh

import (
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perfworker/common"
)

type ErrZh interface {
	Error() string
	ErrorZh() common.ErrMsgZh
	State() dispatcherpb.ComponentState
}

type errZh struct {
	err   error
	msgZh common.ErrMsgZh
}

var ErrMsgZh_state = map[common.ErrMsgZh]dispatcherpb.ComponentState{
	// perfworker
	common.ErrMsgZh_Worker_OthersFailed:           dispatcherpb.ComponentState_Panic,
	common.ErrMsgZh_Worker_PVCCheckFailed:         dispatcherpb.ComponentState_Panic,
	common.ErrMsgZh_Worker_PVCNotFound:            dispatcherpb.ComponentState_Panic,
	common.ErrMsgZh_Worker_ProtoPullFailed:        dispatcherpb.ComponentState_Panic,
	common.ErrMsgZh_Worker_PerfDataGenFailed:      dispatcherpb.ComponentState_Panic,
	common.ErrMsgZh_Worker_AccountPullFailed:      dispatcherpb.ComponentState_Panic,
	common.ErrMsgZh_Worker_AccountUnavailable:     dispatcherpb.ComponentState_Failure,
	common.ErrMsgZh_Worker_JobCreateFailed:        dispatcherpb.ComponentState_Panic,
	common.ErrMsgZh_Worker_PerfTaskSendFailed:     dispatcherpb.ComponentState_Panic,
	common.ErrMsgZh_Worker_PerfResultReportFailed: dispatcherpb.ComponentState_Panic,
	common.ErrMsgZh_Worker_JobWaitCompletedFailed: dispatcherpb.ComponentState_Panic,
	common.ErrMsgZh_Worker_PerfWorkerShutdown:     dispatcherpb.ComponentState_Failure,
	// perftool
	common.ErrMsgZh_Tool_OthersFailed:          dispatcherpb.ComponentState_Failure,
	common.ErrMsgZh_Tool_AllClientCreateFailed: dispatcherpb.ComponentState_Failure,
	common.ErrMsgZh_Tool_AllVULoginFailed:      dispatcherpb.ComponentState_Failure,
}

func NewErrorZh(err error, msgZh common.ErrMsgZh) ErrZh {
	if err, ok := err.(*errZh); ok {
		return err
	}
	return &errZh{
		err:   err,
		msgZh: msgZh,
	}
}

func (e *errZh) Error() string {
	return e.err.Error()
}

func (e *errZh) ErrorZh() common.ErrMsgZh {
	return e.msgZh
}

func (e *errZh) State() dispatcherpb.ComponentState {
	if state, ok := ErrMsgZh_state[e.ErrorZh()]; ok {
		return state
	}
	return dispatcherpb.ComponentState_Invalid
}
