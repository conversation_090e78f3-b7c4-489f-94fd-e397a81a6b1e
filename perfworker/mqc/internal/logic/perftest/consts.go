package perftest

import "time"

const (
	PerfTaskDataPath = "/app/data/perf_task_data"

	ProtobufPath = "protobuf"
	PerfDataPath = "perf_data"
	PerfCasePath = "perf_case"

	TaskResultsKeyPrefix = "task:results"

	pullProtobufLockKey     = "lock:pullProtobuf"
	pullProtobufChannelName = "chan:pullProtobuf"

	defaultPullProtobufTimeout = 10 * time.Minute
	defaultPerfTaskTimeout     = 360 // 360s
)

const (
	GroupJob    = "batch"
	VersionJob  = "v1"
	ResourceJob = "jobs"

	GroupPod    = ""
	VersionPod  = "v1"
	ResourcePod = "pods"

	GroupService    = ""
	VersionService  = "v1"
	ResourceService = "services"

	GroupPvc    = ""
	VersionPvc  = "v1"
	ResourcePvc = "persistentvolumeclaims"

	queryAccountChunkSize    = 500
	queryAccountKeyOfAccount = "account"
	perfDataTitle            = "auth_data|request_data"

	sendTaskDelayTime = 30 * time.Second
)
