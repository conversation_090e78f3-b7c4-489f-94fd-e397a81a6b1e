package common

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestCreateTcpUsbBridge(t *testing.T) {
	tests := []struct {
		name    string
		serial  string
		options []CreateTcpUsbBridgeOption
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid_default_port",
			serial:  "",
			options: nil,
			wantErr: true, // 在测试环境中通常没有真实设备
			errMsg:  "",
		},
		{
			name:   "valid_custom_port",
			serial: "",
			options: []CreateTcpUsbBridgeOption{
				WithPort(5556),
				WithTimeout(10 * time.Second),
			},
			wantErr: true, // 在测试环境中通常没有真实设备
			errMsg:  "",
		},
		{
			name:   "invalid_port_too_low",
			serial: "",
			options: []CreateTcpUsbBridgeOption{
				WithPort(1023),
			},
			wantErr: true,
			errMsg:  "invalid port: 1023, port must be between 1024 and 65535",
		},
		{
			name:   "invalid_port_too_high",
			serial: "",
			options: []CreateTcpUsbBridgeOption{
				WithPort(65536),
			},
			wantErr: true,
			errMsg:  "invalid port: 65536, port must be between 1024 and 65535",
		},
		{
			name:   "valid_port_boundary_low",
			serial: "",
			options: []CreateTcpUsbBridgeOption{
				WithPort(1024),
			},
			wantErr: true, // 在测试环境中通常没有真实设备
			errMsg:  "",
		},
		{
			name:   "valid_port_boundary_high",
			serial: "",
			options: []CreateTcpUsbBridgeOption{
				WithPort(65535),
			},
			wantErr: true, // 在测试环境中通常没有真实设备
			errMsg:  "",
		},
		{
			name:   "custom_timeout",
			serial: "",
			options: []CreateTcpUsbBridgeOption{
				WithTimeout(5 * time.Second),
			},
			wantErr: true, // 在测试环境中通常没有真实设备
			errMsg:  "",
		},
		{
			name:   "specific_serial",
			serial: "test_device_serial",
			options: []CreateTcpUsbBridgeOption{
				WithPort(5557),
			},
			wantErr: true, // 在测试环境中通常没有真实设备
			errMsg:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			address, err := CreateTcpUsbBridge(tt.serial, tt.options...)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
				assert.Empty(t, address)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, address)
				// 验证地址格式
				assert.Regexp(t, `^\d+\.\d+\.\d+\.\d+:\d+$`, address)
			}
		})
	}
}

func TestCreateTcpUsbBridgeOptions(t *testing.T) {
	tests := []struct {
		name            string
		options         []CreateTcpUsbBridgeOption
		expectedPort    int
		expectedTimeout time.Duration
	}{
		{
			name:            "default_options",
			options:         nil,
			expectedPort:    5555,
			expectedTimeout: 30 * time.Second,
		},
		{
			name: "custom_port",
			options: []CreateTcpUsbBridgeOption{
				WithPort(8080),
			},
			expectedPort:    8080,
			expectedTimeout: 30 * time.Second,
		},
		{
			name: "custom_timeout",
			options: []CreateTcpUsbBridgeOption{
				WithTimeout(60 * time.Second),
			},
			expectedPort:    5555,
			expectedTimeout: 60 * time.Second,
		},
		{
			name: "custom_port_and_timeout",
			options: []CreateTcpUsbBridgeOption{
				WithPort(9999),
				WithTimeout(45 * time.Second),
			},
			expectedPort:    9999,
			expectedTimeout: 45 * time.Second,
		},
		{
			name: "multiple_port_options_last_wins",
			options: []CreateTcpUsbBridgeOption{
				WithPort(1111),
				WithPort(2222),
				WithPort(3333),
			},
			expectedPort:    3333,
			expectedTimeout: 30 * time.Second,
		},
		{
			name: "multiple_timeout_options_last_wins",
			options: []CreateTcpUsbBridgeOption{
				WithTimeout(10 * time.Second),
				WithTimeout(20 * time.Second),
				WithTimeout(30 * time.Second),
			},
			expectedPort:    5555,
			expectedTimeout: 30 * time.Second,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			opts := &CreateTcpUsbBridgeOptions{
				Port:    5555,
				Timeout: 30 * time.Second,
			}

			for _, option := range tt.options {
				option(opts)
			}

			assert.Equal(t, tt.expectedPort, opts.Port)
			assert.Equal(t, tt.expectedTimeout, opts.Timeout)
		})
	}
}

func TestWithPort(t *testing.T) {
	tests := []struct {
		name string
		port int
	}{
		{"port_1024", 1024},
		{"port_5555", 5555},
		{"port_8080", 8080},
		{"port_65535", 65535},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			opts := &CreateTcpUsbBridgeOptions{}
			option := WithPort(tt.port)
			option(opts)
			assert.Equal(t, tt.port, opts.Port)
		})
	}
}

func TestWithTimeout(t *testing.T) {
	tests := []struct {
		name    string
		timeout time.Duration
	}{
		{"timeout_5s", 5 * time.Second},
		{"timeout_30s", 30 * time.Second},
		{"timeout_1m", 1 * time.Minute},
		{"timeout_5m", 5 * time.Minute},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			opts := &CreateTcpUsbBridgeOptions{}
			option := WithTimeout(tt.timeout)
			option(opts)
			assert.Equal(t, tt.timeout, opts.Timeout)
		})
	}
}

// 基准测试
func BenchmarkCreateTcpUsbBridgeOptions(b *testing.B) {
	for i := 0; i < b.N; i++ {
		opts := &CreateTcpUsbBridgeOptions{
			Port:    5555,
			Timeout: 30 * time.Second,
		}

		options := []CreateTcpUsbBridgeOption{
			WithPort(8080),
			WithTimeout(60 * time.Second),
		}

		for _, option := range options {
			option(opts)
		}
	}
}
