package monitor

import (
	prommodel "github.com/prometheus/common/model"

	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type (
	apiMetricCache       = map[prommodel.LabelValue]*reporterpb.APIMetric
	responseResultCache  = map[prommodel.LabelValue]*reporterpb.APIMetric_ResponseResult
	responseResultsCache = map[prommodel.LabelValue]responseResultCache
)

type metric struct {
	App            string `json:"app"`
	TaskID         string `json:"task_id"`
	ExecuteID      string `json:"execute_id"`
	Protocol       string `json:"protocol"`
	Name           string `json:"name"`
	Path           string `json:"path"`
	Result         string `json:"result"`
	GRPCStatus     string `json:"grpc_status,omitempty,optional"`
	HTTPStatus     string `json:"http_status,omitempty,optional"`
	BusinessStatus string `json:"business_status,omitempty,optional"`
}
